FROM python:3.12.6-slim-bookworm

# 设置时区
ENV TZ Asia/Shanghai

## 备份原有的 sources.list 文件
RUN touch /etc/apt/sources.list && rm -r /etc/apt/sources.list.d

# 写入阿里云的 apt 源配置
RUN echo "deb https://mirrors.aliyun.com/debian/ bookworm main contrib non-free" > /etc/apt/sources.list \
    && echo "deb https://mirrors.aliyun.com/debian/ bookworm-updates main contrib non-free" >> /etc/apt/sources.list \
    && echo "deb https://mirrors.aliyun.com/debian-security bookworm-security main contrib non-free" >> /etc/apt/sources.list

# 更新 apt
RUN apt-get -y update \
	&& apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    ftp \
    git \
    telnet \
    tzdata \
    tree \
    vim \
    wget \
    zip \
    unzip \
    openssh-client \
    && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone \
    && apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* /usr/share/man/?? /usr/share/man/??_*

## 核心环境变量
# 项目名称
ARG PROJECT_NAME
# 项目根目录
ARG PROJECT_HOME
# 启动模式
ARG MODE

ENV PROJECT_NAME ${PROJECT_NAME}
ENV PROJECT_HOME ${PROJECT_HOME}
ENV MODE ${MODE}

# 创建工作目录
WORKDIR ${PROJECT_HOME}

# 复制所需文件
COPY local/vimrc.template /root/.vimrc

# 克隆代码
ARG BRANCH_NAME

ARG REPOSITORY_NAME
ARG GIT_USERNAME
ARG GIT_PASSWORD
ARG REPOSITORY_URL_BASE

ENV GIT_USERNAME=${GIT_USERNAME}
ENV GIT_PASSWORD=${GIT_PASSWORD}
ENV REPOSITORY_NAME=${REPOSITORY_NAME}
ENV REPOSITORY_URL_BASE=${REPOSITORY_URL_BASE}

RUN git config --global user.name ${GIT_USERNAME}
RUN git config --global credential.helper store

RUN git clone -b ${BRANCH_NAME} ${REPOSITORY_URL_BASE}/${REPOSITORY_NAME}.git .

# 设置python环境变量
ENV PATH ${PROJECT_HOME}/.venv/bin:${PATH}

# uv sync安装依赖
RUN pip install uv==0.6.7 --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple \
    && uv sync

# 暴漏端口
EXPOSE 8080

CMD ["uv", "run", "run.py"]