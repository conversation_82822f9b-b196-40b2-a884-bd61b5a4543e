GIT_USERNAME=
GIT_PASSWORD=
BRANCH_NAME=
REPOSITORY_URL_BASE=
REPOSITORY_NAME=
PROJECT_NAME=
PROJECT_HOME=
MODE=
IMAGE_TAG=
VERSION=

docker build \
  --no-cache \
  --build-arg GIT_USERNAME=${GIT_USERNAME} \
  --build-arg BRANCH_NAME=${BRANCH_NAME} \
  --build-arg REPOSITORY_NAME=${REPOSITORY_NAME} \
  --build-arg GIT_PASSWORD=${GIT_PASSWORD} \
  --build-arg PROJECT_NAME=${PROJECT_NAME} \
  --build-arg PROJECT_HOME={PROJECT_HOME} \
  --build-arg MODE=${MODE} \
  --build-arg REPOSITORY_URL_BASE=${REPOSITORY_URL_BASE} \
  -t ${IMAGE_TAG}:${VERSION} \
  .
