from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse

from rocket_util import BizException

from quickrag.core import manager
from quickrag.api import chat_router, summary_router, tag_router
from quickrag.core.lifespan import lifespan
from quickrag.schema import Response
from quickrag.middleware import CustomHttpMiddleware


# 初始化
manager.init()


def create_app() -> FastAPI:
    # 开发/测试模式下开放api文档链接
    if manager.dev_mode or manager.test_mode:
        app_ = FastAPI(lifespan=lifespan)
    else:
        app_ = FastAPI(redoc_url=None, docs_url=None, lifespan=lifespan)
    return app_


# 创建app
app: FastAPI = create_app()
# 初始化router
app.include_router(chat_router)
app.include_router(summary_router)
app.include_router(tag_router)

# 添加中间件
app.add_middleware(CustomHttpMiddleware) # type: ignore
# 业务类报错处理
@app.exception_handler(BizException)
async def biz_exception_handler(_: Request, exception: BizException):
    if manager.dev_mode:
        import traceback
        traceback.print_exc()

    ret = Response[str]()
    ret.code = exception.code
    ret.message = exception.message
    ret.data = None

    return JSONResponse(status_code=200, content=ret.model_dump())


# 404、405类报错处理
@app.exception_handler(HTTPException)
async def custom_404_handler(_: Request, exception: HTTPException):
    """
    只处理404、405错误
    """
    ret = Response[str]()
    if exception.status_code == 404:
        ret.code = 404
        ret.message = "访问的资源不存在"
        ret.data = None
        return JSONResponse(status_code=200, content=ret.model_dump())
    if exception.status_code == 405:
        ret.code = 405
        ret.message = "Method Not Allowed"
        ret.data = None
        return JSONResponse(status_code=200, content=ret.model_dump())
    return JSONResponse(status_code=exception.status_code, content=exception.detail)
