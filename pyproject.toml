[project]
name = "quickrag"
version = "1.0.1"
description = "极速RAG"
readme = "README.md"
authors = [
    { name = "chenjp105", email = "<EMAIL>" }
]
requires-python = ">=3.12"
dependencies = [
    "aiomysql>=0.2.0",
    "beartype>=0.18.5,<0.19.0",
    "chardet>=5.2.0",
    "cn2an>=0.5.22",
    "datrie>=0.8.2",
    "elasticsearch>=8.18.0,<9.0.0",
    "elasticsearch-dsl>=8.18.0",
    "fastapi>=0.115.3",
    "filelock>=3.15.4",
    "graypy>=2.1.0",
    "hanziconv>=0.3.2",
    "httpx>=0.27.2",
    "json-repair>=0.47.1",
    "langchain-community>=0.3.26",
    "langchain-core>=0.3.66",
    "langchain-openai>=0.3.25",
    "nltk>=3.9.1",
    "numpy>=2.3.0",
    "openai>=1.91.0",
    "pillow>=10.4.0",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.7.1",
    "pyjwt>=2.10.1",
    "python-dotenv>=1.0.1",
    "pycryptodomex>=3.20.0",
    "pycryptodome>=3.20.0",
    "redis>=6.2.0",
    "roman-numbers>=1.0.2",
    "scikit-learn>=1.6.1",
    "sentence-transformers>=3.4.1",
    "sqlalchemy>=2.0.39",
    "starlette>=0.41.0",
    "tiktoken>=0.7.0",
    "uvicorn>=0.30.6",
    "word2number>=1.1",
    "cryptography>=45.0.6",
    "rocket-util @ git+http://10.236.35.71:38012/simba-team/rocket-util.git@master",
    "rocket-nlp @ git+http://10.236.35.71:38012/simba-team/rocket-nlp.git@master",
    "rocket-core @ git+http://10.236.35.71:38012/simba-team/rocket-core.git@master",
    "quick-rag-orm @ git+http://10.236.35.71:38012/simba-team/quick-rag-orm.git@master",
]

[tool.uv]
package = true
index-url = "https://mirrors.aliyun.com/pypi/simple"

[tool.black]
exclude = 'migration|alembic|.venv'