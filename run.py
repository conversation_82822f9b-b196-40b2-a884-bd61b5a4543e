import uvicorn
import argparse


def start():
    parser = argparse.ArgumentParser(description="FastAPI application runner")
    parser.add_argument("--host", type=str, help="Host to bind")
    parser.add_argument("--port", type=int, help="Port to bind")
    parser.add_argument(
        "--reload", action="store_true", help="Enable auto-reload", default=False
    )
    parser.add_argument(
        "--workers", type=int, default=1, help="Number of worker processes"
    )
    parser.add_argument(
        "--app", type=str, default="main:app", help="FastAPI application import string"
    )

    args = parser.parse_args()

    uvicorn.run(
        app=args.app,
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers,
    )


if __name__ == "__main__":
    start()
