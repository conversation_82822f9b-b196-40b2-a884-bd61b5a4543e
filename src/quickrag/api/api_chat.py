from fastapi import APIRouter
from fastapi.responses import StreamingResponse

from rocket_util.common import add_streaming_no_cache_headers

from quickrag.schema import ChatRequest, Block, UseMode
from quickrag.service import CommonChatService, ContextChatService

router = APIRouter(prefix="/chat")


@router.post(
    "/chat", summary="聊天", response_model=list[Block]
)
@add_streaming_no_cache_headers
async def chat(request: ChatRequest) -> StreamingResponse:
    if request.mode == UseMode.AGENT:
        service = ContextChatService(request)
    else:
        service = CommonChatService(request)
    return StreamingResponse(service.chat(), media_type="text/event-stream")
