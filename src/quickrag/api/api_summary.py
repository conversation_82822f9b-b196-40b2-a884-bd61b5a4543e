from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from quickrag.schema import Response, SummaryRequest, SummaryResult
from quickrag.service import SummaryService
from quickrag.core import db_util


router = APIRouter(prefix="/summary")


@router.post(
    "/summary", summary="转专家智能总结"
)
async def summary(request: SummaryRequest, db: AsyncSession = Depends(db_util.db_session)) -> Response[SummaryResult]:
    service = SummaryService()
    res = await service.summary(db, request)
    return Response(data=res)
