from fastapi import APIRouter

from quickrag.schema import AvailableTag, GetAvailableTagRequest, Response
from quickrag.service import TagService


router = APIRouter(prefix="/tag")


@router.post(
    "/get_available_tag_list", summary="获取当前用户可用的标签列表", response_model=Response[list[AvailableTag]]
)
async def chat(request: GetAvailableTagRequest) -> Response[list[AvailableTag]]:
    service = TagService()
    tag_list = await service.query_available_tag_list(request)
    return Response(data=tag_list)
