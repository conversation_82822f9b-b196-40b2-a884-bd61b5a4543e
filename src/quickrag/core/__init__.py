from quickrag.core.manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
from rocket_core.redis_client import RedisClient
from rocket_core.server_conf import (
    ConfManager,
    ESConf,
    DbConf,
    RedisConf,
    BasicConf,
    LogConf,
    JwtConf,
    OpenServiceConf,
    SC,
    ConfEventWatcher,
    AbstractConfEventWatcher,
)
from rocket_nlp.query import FulltextQueryer
from rocket_core.store import ESStore
from qr_orm.db import init_db


manager = QuickRAGManager()
db_util = init_db(manager.get_sub_conf(DbConf))
queryer = FulltextQueryer()
data_store = ESStore(manager.get_sub_conf(ESConf))
redis_client = RedisClient(manager.get_sub_conf(RedisConf))


__all__ = [
    "ConfManager",
    "CacheKey",
    "ESConf",
    "DbConf",
    "RedisConf",
    "BasicConf",
    "LogConf",
    "JwtConf",
    "OpenServiceConf",
    "SC",
    "ConfEventWatcher",
    "AbstractConfEventWatcher",
    "manager",
    "db_util",
    "queryer",
    "data_store",
    "ESStore",
    "redis_client"
]
