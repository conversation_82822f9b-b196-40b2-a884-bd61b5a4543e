import asyncio
from contextlib import asynccontextmanager

from fastapi import FastAPI

from quickrag.core import manager, db_util, data_store, redis_client, <PERSON><PERSON><PERSON><PERSON>
from quickrag.service.load_cache import *


@asynccontextmanager
async def lifespan(app: FastAPI):
    """利用fastapi的lifespan创建表"""
    if manager.dev_mode:
        # 保证orm模块得到加载，否则models模块的加载可能晚于lifespan，导致无法建表
        await create_tables(app)

    if manager.dev_mode or manager.test_mode:
        await set_api_doc_static(app)

    # 启动apollo配置中心轮询
    task = asyncio.create_task(manager.apollo_client.poll())

    # 初始化es
    await data_store.initialize()

    await load_conf_from_db()

    # 初始化redis客户端
    redis_task = await init_redis_client()

    yield

    redis_client.stop_subscription()
    redis_task.cancel()

    await data_store.close()
    task.cancel()


async def create_tables(app: FastAPI): # noqa
    """创建表"""
    # 保证orm模块得到加载，否则models模块的加载可能晚于lifespan，导致无法建表
    from qr_orm import orm # noqa

    async with db_util.engine.begin() as conn:
        await conn.run_sync(db_util.Base.metadata.create_all)


async def set_api_doc_static(app: FastAPI):
    from fastapi.staticfiles import StaticFiles

    static_home = manager.static_home
    app.mount("/static", StaticFiles(directory=static_home), name="static")


async def load_keyword():
    async with db_util.AsyncSessionLocal() as conn:
        info = await LoadTagKeywordService.load(conn)
    manager.reset_cache(CacheKey.TAG_KEYWORD, info)


async def load_area_info():
    async with db_util.AsyncSessionLocal() as conn:
        info = await LoadAreaInfoService.load(conn)
    manager.reset_cache(CacheKey.AREA_INFO, info)


async def load_area_robot_relation():
    async with db_util.AsyncSessionLocal() as conn:
        info = await LoadAreaRobotRelationService.load(conn)
    manager.reset_cache(CacheKey.AREA_ROBOT_RELATION, info)


async def load_qa_robot_conf():
    async with db_util.AsyncSessionLocal() as conn:
        info = await LoadQARobotConfService.load(conn)
    manager.reset_cache(CacheKey.QA_ROBOT, info)


async def load_intent_robot_conf():
    async with db_util.AsyncSessionLocal() as conn:
        info = await LoadIntentRobotConfService.load(conn)
    manager.reset_cache(CacheKey.INTENT_ROBOT, info)


async def load_common_conf():
    async with db_util.AsyncSessionLocal() as conn:
        info = await LoadCommonConfService.load(conn)
    manager.reset_cache(CacheKey.COMMON, info)


async def load_llm_conf():
    async with db_util.AsyncSessionLocal() as conn:
        info = await LoadLLMConfService.load(conn)
    manager.reset_cache(CacheKey.LLM, info)


async def load_intent_map():
    async with db_util.AsyncSessionLocal() as conn:
        info = await LoadIntentMapService.load(conn)
    manager.reset_cache(CacheKey.INTENT, info)


async def load_intent_tool_map():
    async with db_util.AsyncSessionLocal() as conn:
        info = await LoadIntentToolMapService.load(conn)
    manager.reset_cache(CacheKey.INTENT_SCENE_RELATION, info)


async def load_kb_map():
    async with db_util.AsyncSessionLocal() as conn:
        info = await LoadKbMapService.load(conn)
    manager.reset_cache(CacheKey.KB, info)


async def load_conf_from_db():
    # 加载keyword
    await load_keyword()
    # 加载区域信息
    await load_area_info()
    # 加载区域机器人字典
    await load_area_robot_relation()
    # 加载qa机器人配置
    await load_qa_robot_conf()
    # 加载intent机器人配置
    await load_intent_robot_conf()
    # 加载通用配置
    await load_common_conf()
    # 加载llm配置
    await load_llm_conf()
    # 加载意图配置
    await load_intent_map()
    # 加载工具-意图映射数据
    await load_intent_tool_map()
    # 加载知识库数据
    await load_kb_map()


async def init_redis_client():
    from quickrag.sub_executor import executors
    # 初始化redis客户端
    await redis_client.initialize()

    for executor_type, executor in executors.items():
        redis_client.add_executor(executor_type, executor)

    # 开始消费
    task = asyncio.create_task(redis_client.handle_subscription())

    return task
