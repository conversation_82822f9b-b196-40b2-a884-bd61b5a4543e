import os
import logging
from glob import glob
from enum import IntEnum

from pydantic import BaseModel
from sentence_transformers import SentenceTransformer

from rocket_core import Manager, SC
from rocket_util.common import singleton
from quickrag.core.server_conf import AIAuditConf, DbCacheConf, ToolWrapperServiceConf, AIPlatformConf
from quickrag.schema import CacheInfo


class CacheKey(IntEnum):

    """缓存key枚举"""
    TAG_KEYWORD = 0
    AREA_INFO = 1
    AREA_ROBOT_RELATION = 2
    QA_ROBOT = 3
    INTENT_ROBOT = 4
    COMMON = 5
    LLM = 6
    INTENT = 7
    KB = 8
    INTENT_SCENE_RELATION = 9


@singleton
class QuickRAGManager(Manager):

    def __init__(self):
        super().__init__(use_apollo=True)

        # <<< 加载embedding模型 >>>
        self._embedding_model_name: str = os.environ.get("EMBEDDING_MODEL_NAME", "")
        assert self._embedding_model_name, "Embedding模型配置为空！"

        self._embedding_model = self._load_embedding_model()

        # <<< 加载prompt >>>
        self._prompts: dict[str, str] = self._load_prompts()

        # <<< 创建空的缓存 >>>
        self._cache: dict[CacheKey, CacheInfo] = dict()

    def reset_cache(self, key: CacheKey, value: CacheInfo):
        logging.info(f"LoadCache:{value.count_desc}")
        self._cache[key] = value

    def get_cache(self, key: CacheKey) -> BaseModel:
        return self._cache.get(key)

    @property
    def embedding_model(self):
        return self._embedding_model

    @property
    def prompts(self):
        return self._prompts

    def _load_prompts(self) -> dict[str, str]:
        prompt_md_name_list = glob("*.prompt.md", root_dir=self.prompt_home)

        ret = {}
        for prompt_md_name in prompt_md_name_list:
            prompt_md_path = os.path.join(self.prompt_home, prompt_md_name)
            with open(prompt_md_path, "r", encoding="utf-8") as f:
                ret[prompt_md_name.replace(".prompt.md", "")] = f.read()
        return ret

    def _load_embedding_model(self):
        logging.info("Loading vector model...")
        vector_model = SentenceTransformer(
            os.path.join(self._project_dir, "models", self._embedding_model_name)
        )
        logging.info("Vector model loaded.")
        return vector_model

    def _append_custom_sc_conf_list(self) -> list[type[SC]]:
        return [AIAuditConf, DbCacheConf, ToolWrapperServiceConf, AIPlatformConf]
