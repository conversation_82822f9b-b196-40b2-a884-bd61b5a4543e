from pydantic import Field

from rocket_core.server_conf import ServerConf


class AIAuditConf(ServerConf):
    """ai安全网关相关的配置"""
    open_audit: bool = Field(default=False, description="是否启用ai安全网关", alias="ai_audit.open_audit")
    url: str = Field(default="", description="ai安全网关地址", alias="ai_audit.url")
    agent_id: str = Field(default="", description="ai安全网关代理id", alias="ai_audit.agent_id")
    user_type: int = Field(default=2, description="ai安全网关用户类型，1-手机、2-邮箱", alias="ai_audit.user_type")
    timeout: int = Field(default=2, description="ai安全网关请求超时时间", alias="ai_audit.timeout")
    hint: str = Field(default="", description="触发风控的提示语", alias="ai_audit.hint")


class DbCacheConf(ServerConf):
    """数据库缓存相关的配置"""
    use_cache: bool = Field(default=False, description="是否启用数据库缓存", alias="db_cache.use_cache")


class ToolWrapperServiceConf(ServerConf):
    url: str = Field(default="", description="工具包装服务地址", alias="tool_wrapper_service.url")


class AIPlatformConf(ServerConf):
    url: str = Field(default="", description="ai平台地址", alias="ai_platform.url")
    timeout: int = Field(default=600, description="ai平台请求超时时间", alias="ai_platform.timeout")
    sk: str = Field(default="", description="场景密钥", alias="ai_platform.sk")
    username: str = Field(default="", description="用户名称", alias="ai_platform.username")
    team_id: str = Field(default="", description="团队id", alias="ai_platform.team_id")
