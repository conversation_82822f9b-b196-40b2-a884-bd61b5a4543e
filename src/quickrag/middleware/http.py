import logging
from fastapi import Request
from rocket_util.jwt.authentication import Authentication
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.responses import J<PERSON><PERSON>esponse, StreamingResponse

from rocket_util.logging import LogUtil
from rocket_util.common import generate_uuid
from quickrag.core import manager
from quickrag.schema import Block, Code, Response


class CustomHttpMiddleware(BaseHTTPMiddleware):
    stream_api_list = ["/chat/chat"]

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint):
        token = request.headers.get("Authorization", "")
        api_path = request.url.path
        stream = api_path in self.stream_api_list

        # 开发模式下，不进行token校验
        if not manager.dev_mode:
            # 鉴权检查
            yes, auth_failed_response = await self.validate(token, stream)

            if not yes:
                return auth_failed_response

        try:
            params = dict(request.query_params)
            body_params = {}
            content_type = request.headers.get("Content-Type", "")
            if "application/json" in content_type:
                body_params = await request.json()
            elif request.method == "POST":
                form = await request.form()
                body_params = dict(form)
            params.update(body_params)

            # 账号信息对比校验（非开发模式下）
            if not manager.dev_mode and "account" in params and manager.authentication.id != params['account']:
                return await self.fail("用户信息与token中不一致！", stream)

            if "group_id" in params:
                LogUtil.set_group_id(params['group_id'])
            elif "groupId" in params:
                LogUtil.set_group_id(params['groupId'])
            else:
                LogUtil.set_group_id("")

            if api_path:
                LogUtil.set_api_path(api_path)
            else:
                LogUtil.set_api_path("")

            LogUtil.set_req_id(generate_uuid()[-8:])

        except Exception as e:
            logging.error(f"middleware无法获取参数：{str(e)}")

        response = await call_next(request)
        return response

    @classmethod
    async def validate(cls, token: str, stream: bool) -> tuple[bool, JSONResponse | StreamingResponse | None]:
        if not token:
            return False, await cls.fail("请求中token缺失！", stream)

        try:
            parsed = manager.token_helper.parse(token)
            if not parsed:
                return False, await cls.fail("token解析失败！", stream)
            else:
                manager.authentication = Authentication(**parsed)
                return True, None
        except Exception as e:
            return False, await cls.fail(str(e), stream)

    @classmethod
    async def fail(cls, error_msg: str, stream: bool = False):
        if stream:
            # 流式错误返回（SSE格式）
            return StreamingResponse(
                cls.error_stream(error_msg),
                media_type="text/event-stream",
            )
        else:
            # 普通错误返回 JSON
            r = Response[str]()
            r.code = 500
            r.message = error_msg
            r.data = None
            return JSONResponse(status_code=500, content=r.model_dump())

    @classmethod
    async def error_stream(cls, error_msg: str):
        block = Block(code=Code.ERROR, error=error_msg)
        yield f"data: {block.model_dump_json()}\n\n"
