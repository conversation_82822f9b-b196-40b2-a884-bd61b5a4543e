from pydantic import BaseModel, Field
from typing import Optional, Any

from quickrag.schema.schema_base import Scope, IntentLevel, Role, SceneType, LLMType, ModelProtocol, RefType


class CacheInfo(BaseModel):
    count: int = Field(default=0, description="缓存数量")
    count_desc: str = Field(default="", description="缓存数量描述")


"""区域信息信息定义"""
class AreaDTO(BaseModel):
    area_id: int = Field(..., description="区域ID")
    area_name: str = Field(..., description="区域名称")
    scope: Scope = Field(..., description="区域级别")
    province_code: str = Field(..., description="省代码")
    city_code: str = Field(..., description="市代码")
    cb_province_code: str = Field(..., description="cb系统中的省代码")
    cb_city_code: str = Field(..., description="cb系统中的市代码")


class AreaInfo(CacheInfo):
    global_: AreaDTO | None = Field(None, description="全国")
    province: dict[str, AreaDTO] = Field(dict(), description="省")
    city: dict[str, AreaDTO] = Field(dict(), description="市")
    cb_province: dict[str, AreaDTO] = Field(dict(), description="cb系统中的省")
    cb_city: dict[str, AreaDTO] = Field(dict(), description="cb系统中的市")


"""区域-机器人关系信息定义"""
class AreaRobotRelationDTO(BaseModel):
    """机器人配置"""
    area_conf_id: int = Field(..., description="地域配置ID")
    scope: Scope  = Field(..., description="地域级别")
    province_code: str = Field(..., description="地域名称")
    city_code: str = Field(..., description="机器人代码")
    qa_robot_id: int = Field(..., description="机器人ID")
    intent_robot_id: int = Field(..., description="意图机器人ID")


class AreaRobotRelationInfo(CacheInfo):
    """地域与机器人的映射"""
    global_: AreaRobotRelationDTO | None = Field(None, description="全国")
    province: dict[str, AreaRobotRelationDTO] = Field(default_factory=dict, description="省")
    city: dict[str, AreaRobotRelationDTO] = Field(default_factory=dict, description="市")


"""通用信息定义"""
class CommonConfDTO(BaseModel):
    id: int = Field(..., title="id")
    default_llm_id: int = Field(..., title="默认使用的大模型id")


class CommonInfo(CacheInfo):
    """通用信息"""
    common: CommonConfDTO = Field(..., title="通用信息")
    model: Optional[Any] = Field(None, title="模型")


"""意图体系相关的信息定义"""
class IntentDTO(BaseModel):
    id: int = Field(0, description="意图ID")
    name: str = Field("", description="意图名称")
    desc: str = Field("", description="意图描述")
    level: IntentLevel = Field(IntentLevel.LEVEL_2, description="意图级别")
    parent_id: int = Field(0, description="父级意图id")
    category_id: int = Field(0, description="意图分类ID")


class IntentCategoryDTO(BaseModel):
    id: int = Field(0, description="意图分类ID")
    name: str = Field("", description="意图分类名称")
    desc: str = Field("", description="意图分类描述")
    scope: Scope = Field(Scope.PROVINCE, description="意图分类作用域")
    province_code: str = Field("", description="省份编码")
    city_code: str = Field("", description="城市编码")
    target_role: str = Field(Role.USER, description="目标角色")
    tenant_id: str = Field("", description="租户ID")


class IntentInfo(CacheInfo):
    global_category: dict[int, IntentCategoryDTO] = Field(default_factory=dict, description="全局意图分类")
    province_category: dict[str, dict[int, IntentCategoryDTO]] = Field(default_factory=dict, description="省意图分类")
    city_category: dict[str, dict[int, IntentCategoryDTO]] = Field(default_factory=dict, description="地市意图分类")
    level_1_dict: dict[int, IntentDTO] = Field(default_factory=dict, description="1级意图字典")
    level_2_dict: dict[int, IntentDTO] = Field(default_factory=dict, description="2级意图字典")


"""工具相关的信息定义"""
class ToolDTO(BaseModel):
    id: int = Field(0, description="工具ID")
    type: str = Field("", description="工具类型")
    name: str = Field("", description="工具名称")
    desc: str = Field("", description="工具描述")


class ToolSceneDTO(BaseModel):
    id: int = Field(0, description="工具场景ID")
    name: str = Field("", description="工具场景名称")
    desc: str = Field("", description="工具场景描述")
    type: SceneType = Field(SceneType.QUERY, description="工具场景类型")
    tool_id: int = Field(0, description="工具ID")
    code: str = Field("", description="工具场景代码")


class IntentSceneRelationInfo(CacheInfo):
    tools: dict[int, ToolDTO] = Field(default_factory=dict, description="工具字典")
    scenes: dict[int, ToolSceneDTO] = Field(default_factory=dict, description="工具场景字典")
    intent2scene: dict[int, int] = Field(default_factory=dict, description="意图工具映射")


"""意图机器人相关的信息定义"""
class IntentRobotExtraSetting(BaseModel):
    recognization_system_prompt: str = Field("", description="意图识别的提示词")
    recognization_no_confirm_system_prompt: str = Field("", description="意图识别的提示词（无需确认意图）")
    enable_similarity_intent_search: bool = Field(False, description="是否开启相似意图搜索")
    only_user_message: bool = Field(False, description="输出到大模型时，只保留用户消息")
    enable_thinking: bool = Field(False, description="是否启用推理，仅当推理模型时此字段生效")
    enable_ongoing_recognition: bool = Field(False, description="是否启用持续识别，即当持续未识别到意图时，继续识别")
    enable_intent_confirmation: bool = Field(False, description="是否启用意图确认")
    stream: bool = Field(False, description="是否流式输出")
    sort_examples_by_vector: bool = Field(True, description="是否根据向量相似度排序示例")
    intent_def_list_max_size: int = Field(100, description="意图定义列表最大长度")
    recommend_size: int = Field(3, description="推荐意图数量")
    max_memory_chat_msg_size: int = Field(30, description="最大记忆聊天消息数量")
    max_tokens: int = Field(8192, description="增强时送入模型的最大tokens数量")
    field_weight_list: list[str] = Field(
        [
            "content_tks^10",
            "content_ltks^10"
        ],
        description="字段权重列表",
    )
    keyword_min_match: float = Field(0.1, description="关键词匹配阈值")


class IntentRobotDTO(BaseModel):
    id: int = Field(..., description="机器人id")
    name: str = Field(..., description="机器人名称")
    desc: str = Field("", description="机器人描述")
    chat_llm_id: int = Field(..., description="聊天模型id")
    vision_llm_id: int = Field(..., description="聊天模型id")
    chat_llm_setting: dict = Field(default_factory=dict, description="chat大模型配置")
    vision_llm_setting: dict = Field(default_factory=dict, description="vision大模型配置")
    chat_setting: IntentRobotExtraSetting = Field(IntentRobotExtraSetting(), description="聊天大模型额外参数")
    vision_setting: dict = Field(dict, description="视觉大模型额外参数")
    similarity_threshold: float = Field(0.5, description="向量召回相似度阈值")
    vector_similarity_weight: float = Field(0.8, description="召回时，向量部分的权重")
    top_n: int = Field(10, description="召回的topN")
    top_k: int = Field(1024, description="向量召回的knn半径")
    tenant_id: str = Field("", description="租户id")
    category_ids: list[int] = Field(None, description="本机器人绑定的额外意图类别id列表")


class IntentRobotInfo(CacheInfo):
    robots: dict[int, IntentRobotDTO] = Field(default_factory=dict, description="机器人")
    models: dict[int, Any] = Field(default_factory=dict, description="模型缓存")


"""问答相关的信息定义"""
class Parameter(BaseModel):
    name: str = Field("", description="参数名")
    optional: bool = Field(False, description="是否可选")


class TypeWeight(BaseModel):
    faq: float = Field(1.0, description="问答知识类型知识的权重系数")
    document: float = Field(1.0, description="文档类型知识的权重系数")
    history: float = Field(1.0, description="会话类型知识的权重系数")


class ScopeWeight(BaseModel):
    global_: float = Field(1.0, description="全国知识权重，默认是1.0即不提权")
    province: float = Field(1.0, description="省分知识权重，默认是1.0即不提权")
    city: float = Field(1.0, description="地市知识，默认是1.0即不提权")


class WeightGroup(BaseModel):
    type: TypeWeight = Field(TypeWeight(), description="知识类别相关的权重")
    scope: ScopeWeight = Field(ScopeWeight(), description="知识范围相关的权重")


class QAChatExtraSetting(BaseModel):
    """qa机器人-语言模型的相关配置信息"""
    rag_prompt_system: str = Field(
        "你是一个智能助手，请总结知识库的内容来回答问题，请列举知识库中的数据详细回答。当所有知识库内容都与问题无关时，你的回答必须包括“知识库中未找到您要的答案！”这句话。回答需要考虑聊天历史以下是知识库：\n{knowledge}\n以上是知识库。",
        description="系统提示词",
    )
    solo_prompt_system: str = Field("请基于聊天内容回复", description="单聊时的提示词")
    max_tokens: int = Field(8192, description="增强时送入模型的最大tokens数量")
    parameters: list[Parameter] = Field(
        None,
        description="参数描述：除了knowledge外的其他参数",
    )
    enable_kg: bool = Field(False, description="是否开启知识图谱")
    enable_tag: bool = Field(False, description="是否开启基于标签的搜索")
    enable_question_gen: bool = Field(False, description="自动生成问题")
    enable_citation: bool = Field(True, description="是否开启引用标注")
    enable_thinking: bool = Field(False, description="是否启用推理，仅当推理模型时此字段生效")
    latest_question_n: int = Field(3, description="最近n用户提问消息")
    latest_message_n: int = Field(6, description="最近n条消息（送给大模型）")
    weight_group: WeightGroup= Field(WeightGroup(), description="权重系数")
    only_user_message: bool = Field(False, description="输出到大模型时，只保留用户消息")
    keyword_weight: float = Field(0.1, description="关键词检索路的权重占比")
    field_weight_list: list[str] = Field(
        [
            "title_tks^10",
            "title_sm_tks^5",
            "important_kwd^30",
            "important_tks^20",
            "question_tks^20",
            "content_ltks^2",
            "content_sm_ltks^1",
        ],
        description="关键词路-检索字段所占权重"
    )
    keyword_min_match: float = Field(0.3, description="关键词匹配阈值")


class QARobotDTO(BaseModel):
    id: int = Field(..., description="机器人id")
    name: str = Field(..., description="机器人名称")
    desc: str = Field("", description="机器人描述")
    chat_llm_id: int = Field(..., description="聊天模型id")
    vision_llm_id: int = Field(..., description="聊天模型id")
    chat_llm_setting: dict = Field(default_factory=dict, description="chat大模型配置")
    vision_llm_setting: dict = Field(default_factory=dict, description="vision大模型配置")
    chat_setting: QAChatExtraSetting= Field(QAChatExtraSetting(), description="聊天大模型额外参数")
    vision_setting: dict = Field(dict, description="视觉大模型额外参数")
    enable_llm_solo: bool = Field(True, description="是否启用大模型单聊")
    prologue: str = Field("", description="开场白")
    empty_response: str = Field("", description="默认回复内容")
    similarity_threshold: float = Field(0.5, description="向量召回相似度阈值")
    rerank_threshold: float = Field(0.4, description="重排序阈值")
    vector_similarity_weight: float = Field(0.8, description="召回时，向量部分的权重")
    top_n: int = Field(10, description="召回的topN")
    top_k: int = Field(1024, description="向量召回的knn半径")
    do_refer: bool = Field(True, description="是否让模型标注引用")
    tenant_id: str = Field("", description="租户id")
    kb_ids: list[str] = Field(None, description="本机器人绑定的额外知识库id列表")


class QARobotInfo(CacheInfo):
    robots: dict[int, QARobotDTO] = Field(default_factory=dict, description="机器人")
    models: dict[int, Any] = Field(default_factory=dict, description="模型缓存")


"""LLM相关的信息定义"""
class LlmDTO(BaseModel):
    id: int = Field(..., description="id")
    llm_type: LLMType = Field(LLMType.CHAT, description="模型类型")
    alias: str = Field(..., description="别名")
    name: str = Field(..., description="名称")
    openai_api_base: str = Field(..., description="模型基础地址")
    openai_api_key: str = Field(..., description="模型密钥")
    with_thinking: bool = Field(False, description="是否是推理模型")
    extra: dict = Field({}, description="额外的配置参数")
    protocol: ModelProtocol = Field(ModelProtocol.OPENAI_CHAT, description="模型协议")


class LlmInfo(CacheInfo):
    llms: dict[int, LlmDTO] = Field(default_factory=dict, description="模型")


"""TagKeyword信息相关的定义"""
class KeywordDTO(BaseModel):
    keyword_id: int = Field(0, description="关键词id")
    tag_id: int = Field(0, description="关联的标签id")
    keyword: str = Field("", description="关键词")
    category: str = Field("", description="类别")
    case_sensitive: bool = Field(False, description="是否区分大小写")


class TagDTO(BaseModel):
    tag_id: int = Field(0, description="关联的标签id")
    tag_name: str = Field("", description="关联的标签名称")
    tag_desc: str = Field("", description="关联的标签描述")
    scope: int = Field(0, description="作用域")
    province_code: str = Field("", description="省份编码")
    city_code: str = Field("", description="地市编码")


class TagMapDTO(BaseModel):
    global_: dict[int, TagDTO] = Field(default_factory=dict, description="全国有效标签集合")
    province: dict[str, dict[int, TagDTO]] = Field(default_factory=dict, description="省份有效标签集合")
    city: dict[str, dict[int, TagDTO]] = Field(default_factory=dict, description="地市有效标签集合")


class KeywordMapDTO(BaseModel):
    global_: dict[int, KeywordDTO] = Field(default_factory=dict, description="全国标签关键字")
    province: dict[str, dict[int, KeywordDTO]] = Field(default_factory=dict, description="省分标签关键字")
    city: dict[str, dict[int, KeywordDTO]] = Field(default_factory=dict, description="地市标签关键字")


class TagKeywordInfo(CacheInfo):
    tag_map: TagMapDTO = Field(default=TagMapDTO(), description="标签映射")
    keyword_map: KeywordMapDTO = Field(default=KeywordMapDTO(), description="关键字映射")


"""知识库信息相关的定义"""
class KbDTO(BaseModel):
    id: str = Field("", description="知识库id")
    type: RefType = Field(RefType.FAQ, description="知识库类型")
    domain: str = Field("", description="知识库域")
    scope: Scope = Field(Scope.GLOBAL, description="知识库范围")
    province_code: str = Field("", description="省份编码")
    city_code: str = Field("", description="地市编码")
    tenant_id: str = Field("", description="租户id")
    name: Optional[str] = Field("", description="知识库名称")
    desc: Optional[str] = Field("", description="知识库描述")
    similarity_threshold: float = Field(0.5, description="相似度阈值")
    vector_similarity_weight: float = Field(0.8, description="向量相似度权重")
    weight: float = Field(1.0, description="权重")
    special_tag_ids: list[int] = Field(default_factory=list, description="特殊标签id列表")


class KbInfo(CacheInfo):
    global_: dict[str, KbDTO] = Field(default_factory=dict, description="全国")
    province: dict[str, dict[str, KbDTO]] = Field(default_factory=dict, description="省分")
    city: dict[str, dict[str, KbDTO]] = Field(default_factory=dict, description="地市")


__all__ = [
    "CacheInfo",
    "AreaDTO",
    "AreaInfo",
    "AreaRobotRelationDTO",
    "AreaRobotRelationInfo",
    "CommonConfDTO",
    "CommonInfo",
    "IntentDTO",
    "IntentCategoryDTO",
    "IntentInfo",
    "ToolDTO",
    "ToolSceneDTO",
    "IntentSceneRelationInfo",
    "IntentRobotExtraSetting",
    "IntentRobotDTO",
    "IntentRobotInfo",
    "Parameter",
    "TypeWeight",
    "ScopeWeight",
    "WeightGroup",
    "QAChatExtraSetting",
    "QARobotDTO",
    "QARobotInfo",
    "LlmDTO",
    "LlmInfo",
    "TagKeywordInfo",
    "TagDTO",
    "TagMapDTO",
    "KeywordDTO",
    "KeywordMapDTO",
    "KbDTO",
    "KbInfo",
]