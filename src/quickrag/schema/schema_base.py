from enum import IntEnum, StrEnum
from typing import TypeVar, Generic, Optional, Any, List, Union, Dict, Tuple

import numpy as np

from pydantic import BaseModel, Field, field_validator, model_validator
from langchain_openai import ChatOpenAI

from rocket_util import ChinaUnicomOpenServiceChatModel


DEFAULT_MATCH_VECTOR_TOPN = 10
DEFAULT_MATCH_SPARSE_TOPN = 10
VEC = list | np.ndarray

T = TypeVar("T")

# 合法的返回码
SUCCESS_CODE = 200

# 总部省分编码
ZB_PROVINCE_CODE = "100000"
# 全国省分编码（虚拟）
GLOBAL_PROVINCE_CODE = "000000"


class Response(BaseModel, Generic[T]):
    code: int = Field(default=200, description="消息编码")
    message: str = Field(default="成功", description="异常信息")
    data: T = Field(default=None, description="消息体")

    @field_validator("message") # noqa
    @classmethod
    def message_validator(cls, v):
        """
        统一msg与message字段
        """
        if "msg" in v:
            return v["msg"]
        elif "message" in v:
            return v["message"]
        else:
            return ""


class Code(IntEnum):
    SUCCESS = 200
    ERROR = 500


class Role(StrEnum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    EXPERT = "expert"


class Scope(IntEnum):
    """地域级别"""
    GLOBAL = 0
    PROVINCE = 1
    CITY = 2


class RefType(StrEnum):
    FAQ = "faq"
    DOCUMENT = "document"
    HISTORY = "history"


class LLMType(StrEnum):
    CHAT = "chat"
    VISION = "vision"


class IntentLevel(IntEnum):
    LEVEL_1 = 1
    LEVEL_2 = 2


class ModelProtocol(StrEnum):
    """语言模型协议"""
    OPENAI_CHAT = "openai-chat"
    CHINAUNICOM_OPEN_SERVICE = "chinaunicom-open-service"


class SceneType(StrEnum):
    """工具场景类型"""
    QUERY = "query"
    MODIFY = "modify"
    OTHER = "other"


ChatModel = ChatOpenAI | ChinaUnicomOpenServiceChatModel


class LLMHandler(BaseModel):
    chat_model: Optional[ChatModel] = Field(default=None, description="语言模型")
    chat_model_conf: Any = Field(default=None, description="语言模型配置")
    vision_model: Optional[ChatModel] = Field(default=None, description="视觉模型")
    vision_model_conf: Any = Field(default=None, description="视觉模型配置")


class SparseVector(BaseModel):
    indices: List[int] = Field(..., description="稀疏向量中的索引列表")
    values: Optional[Union[List[float], List[int]]] = Field(
        None, description="与 indices 对应的值，长度需一致；可以为 None"
    )

    @model_validator(mode="after")
    def validate_length(self):
        if self.values is not None and len(self.indices) != len(self.values):
            raise ValueError("indices 和 values 长度不一致")
        return self


class MatchTextExpr(BaseModel):
    fields: List[str] = Field(..., description="用于匹配的字段列表")
    matching_text: str = Field(..., description="匹配使用的文本")
    topn: int = Field(..., description="返回的 Top N 结果数")
    extra_options: Optional[Dict] = Field(None, description="额外选项")


class MatchDenseExpr(BaseModel):
    vector_column_name: str = Field(..., description="向量所在的列名")
    embedding_data: Any = Field(..., description="嵌入向量数据")
    embedding_data_type: str = Field(..., description="嵌入数据类型")
    distance_type: str = Field(..., description="距离度量方式")
    topn: int = Field(DEFAULT_MATCH_VECTOR_TOPN, description="返回的 Top N 结果数")
    extra_options: Optional[Dict] = Field(None, description="额外选项")

    @field_validator('embedding_data') # noqa
    @classmethod
    def validate_embedding_data(cls, v):
        if not isinstance(v, (list, np.ndarray)):
            raise TypeError('embedding_data 必须是 list 或 numpy.ndarray')
        return v


class MatchSparseExpr(BaseModel):
    vector_column_name: str = Field(..., description="稀疏向量列名")
    sparse_data: Union[SparseVector, Dict] = Field(..., description="稀疏向量数据或字典")
    distance_type: str = Field(..., description="距离度量方式")
    topn: int = Field(..., description="返回的 Top N 结果数")
    opt_params: Optional[Dict] = Field(None, description="额外参数")


class MatchTensorExpr(BaseModel):
    column_name: str = Field(..., description="张量列名")
    query_data: Any = Field(..., description="查询张量数据")
    query_data_type: str = Field(..., description="查询数据类型")
    topn: int = Field(..., description="返回的 Top N 结果数")
    extra_option: Optional[Dict] = Field(None, description="额外选项")

    @field_validator('query_data') # noqa
    @classmethod
    def validate_embedding_data(cls, v):
        if not isinstance(v, (list, np.ndarray)):
            raise TypeError('embedding_data 必须是 list 或 numpy.ndarray')
        return v


class FusionExpr(BaseModel):
    method: str = Field(..., description="融合方法名称")
    topn: int = Field(..., description="返回的 Top N 结果数")
    fusion_params: Optional[Dict] = Field(None, description="融合方法的额外参数")


MatchExpr = Union[
    MatchTextExpr,
    MatchDenseExpr,
    MatchSparseExpr,
    MatchTensorExpr,
    FusionExpr,
]


class OrderByExpr(BaseModel):
    fields: List[Tuple[str, int]] = Field(default_factory=list, description="排序字段及顺序（0 升序，1 降序）")

    def asc(self, field: str):
        self.fields.append((field, 0))
        return self

    def desc(self, field: str):
        self.fields.append((field, 1))
        return self

    def get_fields(self):
        return self.fields


__all__ = [
    "DEFAULT_MATCH_VECTOR_TOPN",
    "DEFAULT_MATCH_SPARSE_TOPN",
    "VEC",
    "T",
    "SUCCESS_CODE",
    "ZB_PROVINCE_CODE",
    "GLOBAL_PROVINCE_CODE",
    "Response",
    "Code",
    "Role",
    "Scope",
    "LLMType",
    "IntentLevel",
    "ModelProtocol",
    "SceneType",
    "ChatModel",
    "LLMHandler",
    "SparseVector",
    "MatchTextExpr",
    "MatchDenseExpr",
    "MatchSparseExpr",
    "MatchTensorExpr",
    "FusionExpr",
    "MatchExpr",
    "OrderByExpr",
    "RefType"
]