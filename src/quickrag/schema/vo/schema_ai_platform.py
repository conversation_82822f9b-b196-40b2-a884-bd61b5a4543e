from enum import StrEnum

from pydantic import BaseModel, Field


class CreateTaskRequest(BaseModel):
    """创建任务的请求参数模型"""
    group_id: str = Field(..., max_length=128, description="会话ID")
    province_code: str = Field(..., max_length=64, description="省分编码")
    city_code: str = Field(..., max_length=64, description="城市编码")
    account: str = Field(..., max_length=128, description="账号")
    role: str = Field(..., max_length=32, description="执行角色：一线-user, 专家-expert")
    app_id: str = Field("", max_length=128, description="Agent/Workflow流的app_id")


class CreateTaskResponse(BaseModel):
    """创建任务的响应结果模型"""
    app_task_id: int = Field(..., description="任务id，由系统生成的主键")
    create_date: str = Field(..., description="任务创建时间")


class AITaskRequest(BaseModel):
    model_config = {
        "populate_by_name": True
    }
    content: str = Field(..., description="内容")
    app_task_id: str = Field(..., description="应用task-id", alias="conversationId")
    app_id: str = Field(..., description="场景应用id", alias="appId")
    team_id: str = Field(..., description="团队id", alias="teamId")
    timestamp: str = Field(..., description="时间戳")
    variables: dict[str, str] = Field(default_factory=dict, description="变量", alias="variable")
    sys_variables: dict[str, str] = Field(default_factory=dict, description="系统变量", alias="sysVariable")
    access_token: str = Field(..., description="访问令牌", alias="accessToken")
    username: str = Field(..., description="用户名称", alias="userName")
    stream: bool = Field(default=True, description="是否流式返回结果")


class ExecuteInfo(BaseModel):
    """execute过程信息"""
    loading_title: str = Field("", description="当前执行环节名称", alias="loadingTitle")
    task_target: str = Field("", description="taskTarget", alias="taskTarget")
    finished: bool = Field(default=True, description="是否结束，流式中使用")


class Rich(BaseModel):
    format_flag: str = Field("", description="多模态类型", alias="formatFlag")
    text: str = Field("", description="多模态值")


class ResultInfo(BaseModel):
    content: str = Field("", description="智能体响应内容，按token流式输出")
    agent_session: str = Field("", description="智能体执行多模态列表标识，>1表示有多模态数据", alias="agentSession")
    rich_list: list[Rich] = Field(
        default_factory=list,
        description="智能体多模态输出结果集，流式接口只有在多模态的输出的时间点输出（类流式），需客户端进行读取记录",
        alias="richList"
    )


class AITaskResponse(BaseModel):
    code: str = Field("", description="状态码", alias="respCode")
    desc: str = Field("", description="状态描述", alias="respDesc")
    app_task_id: str = Field("", description="应用task-id", alias="conversationId")
    msg_id: str = Field("", description="消息id", alias="messageId")
    execute: ExecuteInfo = Field(default=ExecuteInfo(), description="执行信息")
    result: ResultInfo = Field(default=ResultInfo(), description="结果信息")


class CommonSysVariables(BaseModel):
    app_task_id: str = Field(..., description="场景task-id")
    token: str = Field(..., description="业务访问令牌")
    province_code: str = Field(..., description="cb省分编码")
    city_code: str = Field(..., description="cb地市编码")


class AuthMethod(StrEnum):
    """授权类型"""
    OFFICIAL = "official"
    CUSTOM = "custom"


class AIPlatformAuthData(BaseModel):
    auth_method: AuthMethod = Field(default=AuthMethod.OFFICIAL, description="授权方式")
    sk: str = Field(..., description="场景密钥", alias="sk")
    username: str = Field(..., description="用户名称", alias="username")
    team_id: str = Field(..., description="团队id", alias="team_id")


__all__ = [
    "CreateTaskResponse",
    "CreateTaskRequest",
    "AITaskRequest",
    "AITaskResponse",
    "CommonSysVariables",
    "AIPlatformAuthData",
]
