from enum import StrEnum, IntEnum
from typing import Optional

from pydantic import BaseModel, Field
from quickrag.schema.schema_base import Code, Role, RefType


class UseMode(StrEnum):
    """问答使用模式"""
    # 纯问答
    QA = "qa"
    # 纯意图
    INTENT = "intent"
    # 意图/问答并行
    BOTH = "both"
    # agent模式，先调用意图，根据意图结果进行问答或者agent执行
    AGENT = "agent"


class GenerationMode(StrEnum):
    """块内容生成的模式，要区分qa和intent"""

    ## 一、QA部分使用的模式
    # 1 引用自知识库
    REF_KB = "ref_kb"
    # 2 来自大模型的直接回答
    BY_LLM = "by_llm"
    # 3 qa-的默认话术，非大模型返回
    DEFAULT = "default"

    ## 二、Intent部分使用的模式
    # 1. 意图识别
    INTENT = "intent"

    ## 三、Risk部分使用的模式
    RISK_QUESTION = "risk_question"
    RISK_ANSWER = "risk_answer"


class BlockSource(StrEnum):
    INTENT = "intent"
    QA = "qa"
    RISK = "risk"


class IntentStatus(IntEnum):
    """意图识别状态"""
    # 不匹配
    NO_MATCH = 0
    # 模糊匹配
    FUZZY = 1
    # 精确匹配
    LOCKED = 2
    # 意图已确认
    CONFIRMED = 3


class SceneType(StrEnum):
    """意图识别状态"""
    Query = "query"
    MODIFY = "modify"
    OTHER = "other"


class GroupExtra(BaseModel):
    group_id: str = Field("", description="业务会话id")


class KnowledgeExtra(BaseModel):
    knowledge_id: str = Field("", description="知识id")
    knowledge_type: str = Field("", description="知识类型：例如wiki代表rich-text")
    knowledge_domain: str = Field("", description="知识域：例如-B域-business")


Extra = GroupExtra | KnowledgeExtra


class Message(BaseModel):
    """消息"""
    message_id: str = Field("", description="消息id")
    created_at: str = Field("", description="消息创建时间")
    content: str = Field("", description="消息内容")
    role: Role = Field(default=Role.USER, description="消息角色")
    epoch: int = Field(default=0, description="当前对话的轮次")


class SystemMessage(Message):
    """系统消息"""
    pass


class UserMessage(Message):
    """用户消息"""
    pass


class ExpertMessage(UserMessage):
    """专家消息"""
    pass


class AssistantMessage(Message):
    """AI助手消息"""
    generation_mode: Optional[GenerationMode] = Field(None, description="intent块内容生成的模式")
    consumed_time: int = Field(default=0, description="消耗的时间（毫秒）")


class ReplyMessageFragment(BaseModel):
    """消息片段，只用于Block返回"""
    message_id: str = Field("", description="消息id")
    created_at: str = Field("", description="消息创建时间")
    content: str = Field("", description="消息内容")
    role: Role = Field(default=Role.USER, description="消息角色")


class ChatRequest(BaseModel):
    group_id: str = Field(..., description="业务会话id")
    account: str = Field(..., description="用户账号/工号")
    query: str = Field(..., description="消息内容，目前只支持文本消息")
    province_code: str = Field("", description="省份编码")
    city_code: str = Field("", description="地市编码")
    role: Role = Field(..., description="消息角色")
    tag_id_list: list[int] = Field(None, description="指定的标签id列表")
    email: str = Field("", description="用户邮箱，用作AI安全网关审计")
    mode: UseMode = Field(UseMode.QA, description="使用模式")


class ChunkReference(BaseModel):
    chunk_id: str = Field("", description="es中的-块id")
    document_id: str = Field("", description="es中的-块所在的文档id")
    document_name: str = Field("", description="es中的-块所在的文档名称")
    kb_id: str = Field("", description="es中-块所在的知识库id")
    ref_type: RefType = Field(RefType.DOCUMENT, description="引用数据的来源类型")
    content: str = Field("", description="es中-块内容")
    image_id: str = Field("", description="es中-块所涉及的图片id")
    positions: list[int] = Field(None, description="es中-块位置")
    extra: Extra = Field(None, description="扩展信息")
    quoted: bool = Field(True, description="总结的回复中是否实际引用")


class DocumentReference(BaseModel):
    document_id: str = Field("", description="es中的-文档id")
    document_name: str = Field("", description="es中的-块所在的文档名称")
    ref_type: RefType = Field(RefType.DOCUMENT, description="引用数据的来源类型")
    extra: Extra = Field(None, description="扩展信息")
    count: int = Field(0, description="引用块的个数")


class BlockReference(BaseModel):
    chunk_reference_list: list[ChunkReference] = Field(None, description="块引用列表")
    document_reference_list: list[DocumentReference] = Field(None, description="文档引用列表")


class RecommendTool(BaseModel):
    tool_id: str = Field("", description="工具id")
    tool_type: str = Field("", description="工具类型")
    tool_name: str = Field("", description="工具名称")
    scene_id: str = Field("", description="场景id")
    scene_code: str = Field("", description="场景编码")
    scene_name: str = Field("", description="场景名称")
    scene_type: SceneType = Field(SceneType.Query, description="场景类型")


class RecommendItem(BaseModel):
    intent_id: str = Field("0", description="意图id")
    intent_name: str = Field("", description="意图名称")
    content: str = Field("", description="典型问法")
    tool: Optional[RecommendTool] = Field(None, description="意图相关的工具")


RECOGNITION_CODE_DESC = f"""
    通过分析聊天消息得到的意图识别状态码：
    {IntentStatus.NO_MATCH}-未识别，表示与所有意图都无关
    {IntentStatus.FUZZY}-模糊匹配，表示与一个或多个意图存在模糊匹配，即相关但不够明确，需要进一步确认；
    {IntentStatus.LOCKED}-精确匹配，表示与某一个意图的关键词、问法、语义、细节等完全一致；
    {IntentStatus.CONFIRMED}-意图确认，表示用户已明确确认意图，要求上轮意图识别结果状态必须为2（精确匹配）
"""


class IntentRecommend(BaseModel):
    recognized_status: IntentStatus = Field(IntentStatus.NO_MATCH, description=RECOGNITION_CODE_DESC)
    recognized_intent_id: Optional[str] = Field("0", description="意图id")
    recognized_intent_name: Optional[str] = Field("", description="意图名称")
    content: Optional[str] = Field("", description="完整的回复内容")
    recommend_list: list[RecommendItem] = Field(default_factory=list, description="推荐的示例列表")


BlockExtra = BlockReference | IntentRecommend


class Block(BaseModel):
    code: Code = Field(Code.SUCCESS, description="状态码：200表示成功, 500表示失败")
    error: str = Field("", description="错误信息，为空表示无错误")
    message: ReplyMessageFragment = Field(None, description="回复消息片段")
    conversation_id: str = Field("", description="会话id, 业务会话id")
    group_id: str = Field("", description="在线咨询会话id")
    extra: Optional[BlockExtra] = Field(None, description="扩展信息")
    source: BlockSource = Field(BlockSource.QA, description="消息来源，intent表示意图块，qa表示问答块")
    finished: bool = Field(False, description="是否为终止块")
    generation_mode: Optional[GenerationMode] = Field(None, description="生成模式，只有最后一个块才有值，其余为None")


__all__ = [
    "UseMode",
    "GenerationMode",
    "BlockSource",
    "RefType",
    "IntentStatus",
    "SceneType",
    "GroupExtra",
    "KnowledgeExtra",
    "Extra",
    "Message",
    "SystemMessage",
    "UserMessage",
    "ExpertMessage",
    "AssistantMessage",
    "ReplyMessageFragment",
    "ChatRequest",
    "ChunkReference",
    "DocumentReference",
    "BlockReference",
    "RecommendTool",
    "RecommendItem",
    "RECOGNITION_CODE_DESC",
    "IntentRecommend",
    "BlockExtra",
    "Block",
]