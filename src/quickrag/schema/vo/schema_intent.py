from pydantic import BaseModel, Field, model_validator
from quickrag.schema.vo.schema_chat import IntentStatus, RECOGNITION_CODE_DESC


class IntentChunk(BaseModel):
    category_id: str = Field("")
    intent_data_id: str = Field("")

    content_tks: str = Field("")
    content_ltks: str = Field("")

    content_with_weight: str = Field("")

    intent_level_1_id: str = Field("")
    intent_level_1_name: str = Field("")
    intent_level_1_desc: str = Field("")

    intent_level_2_id: str = Field("")
    intent_level_2_name: str = Field("")
    intent_level_2_desc: str = Field("")


class IntentRetrievalResult(BaseModel):
    total: int = Field(default=0, description="总数")
    chunks: list[IntentChunk] = Field(default_factory=list, description="知识块列表")


class IntentDefItem(BaseModel):
    intent_level_1_id: str = Field(..., description="一级意图ID")
    intent_id: str = Field(..., description="二级意图ID")
    intent_name: str = Field("", description="意图名称")
    intent_desc: str = Field("", description="意图描述")
    example_questions: list[str] = Field(default_factory=list, description="典型问法列表")
    epoch: int = Field(..., description="当前epoch编号")


RECOGNITION_INTENT_NUMBERS_DESC = f"""
    该字段表示相关的意图编码列表：
    状态码为{IntentStatus.NO_MATCH}时，为[]，表没有任何关联的意图编号；
    状态码为{IntentStatus.FUZZY}时，为一个或多个模糊匹配关联的意图编号；
    状态码为{IntentStatus.LOCKED}时，有且仅有一个意图编号，表示精确匹配的意图编号；
    状态码为{IntentStatus.CONFIRMED}时，有且仅有一个意图编号，表示已确认的意图编号（与上轮精确匹配的意图编号一致）。
"""


class IntentRecognitionResult(BaseModel):
    recognized_status: IntentStatus = Field(IntentStatus.NO_MATCH, description=RECOGNITION_CODE_DESC)
    related_intent_numbers: list[int] = Field(default_factory=list, description=RECOGNITION_INTENT_NUMBERS_DESC)
    content: str = Field("", description="回复内容，参照回复要求与示例。")

    @model_validator(mode="after")
    def validate_intent_id_for_status(self) -> "IntentRecognitionResult":
        if self.recognized_status in (IntentStatus.LOCKED, IntentStatus.CONFIRMED):
            if not self.related_intent_numbers and not len(self.related_intent_numbers) == 1:
                raise ValueError("当 recognized_status 为2,3时，matched_intent_number必须存在")
        return self


__all__ = [
    "IntentChunk",
    "IntentRetrievalResult",
    "IntentDefItem",
    "RECOGNITION_INTENT_NUMBERS_DESC",
    "IntentRecognitionResult",
]