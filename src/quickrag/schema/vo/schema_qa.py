from pydantic import BaseModel, Field


class QAChunk(BaseModel):
    chunk_id: str = Field("")
    content_ltks: str = Field("")
    content_with_weight: str = Field("")
    doc_id: str = Field("")
    doc_kwd: str = Field("")
    kb_id: str = Field("")
    important_kwd: list[str] = Field("")
    image_id: str = Field("")
    similarity: float = Field(0.0)
    vector_similarity: float = Field(0.0)
    term_similarity: float = Field(0.0)
    vector: list[float] = Field(None)
    positions: list[list[int]] | list[int] = Field(None)
    highlight: str = Field("")
    kb_type: str = Field("")
    extra: dict = Field(None)


class QADoc(BaseModel):
    doc_id: str = Field("")
    doc_name: str = Field("")
    kb_type: str = Field("")
    extra: dict = Field("")
    count: int = Field(0)


class QARetrievalResult(BaseModel):
    total: int = Field(0)
    chunks: list[QAChunk] = Field(None)


class QASearchResult(BaseModel):
    total: int = Field(0)
    chunk_ids: list[str] = Field(None)
    query_vector: list[float] = Field(None)
    data: dict = Field(None)
    keywords: list[str] = Field(None)


class QASearchReq(BaseModel):
    kb_ids: list[str] = Field(None)
    question: str = Field("")
    topk: int = Field(1024)
    similarity: float = Field(0.3)
    available_int: int = Field(1)
    limit: int = Field(128)


__all__ = [
    "QAChunk",
    "QADoc",
    "QARetrievalResult",
    "QASearchResult",
    "QASearchReq",
]