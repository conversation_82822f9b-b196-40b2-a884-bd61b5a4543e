from pydantic import BaseModel, Field


class SummaryRequest(BaseModel):
    group_id: str = Field(..., description="业务会话id")
    account: str = Field(..., description="用户账号/工号")
    province_code: str = Field("", description="省份编码")
    city_code: str = Field("", description="地市编码")
    msg_list: list[str] = Field(None, description="消息内容，目前只支持文本消息")


class SummaryResult(BaseModel):
    title: str = Field("", description="标题")
    content: str = Field("", description="内容")
    biz_number: str = Field("", description="业务单号")


__all__ = [
    "SummaryRequest",
    "SummaryResult",
]