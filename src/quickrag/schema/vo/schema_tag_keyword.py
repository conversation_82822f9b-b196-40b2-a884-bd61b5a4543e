from pydantic import BaseModel, Field

from quickrag.schema.schema_base import Role


class GetAvailableTagRequest(BaseModel):
    group_id: str = Field(..., description="业务会话id")
    account: str = Field(..., description="用户账号/工号")
    province_code: str = Field(..., description="省份编码")
    city_code: str = Field(..., description="地市编码")
    role: Role = Field(..., description="消息角色")


class AvailableTag(BaseModel):
    tag_id: str = Field(..., description="标签id")
    tag_name: str = Field(..., description="标签名称")
    tag_desc: str = Field(..., description="标签描述")


__all__ = [
    "GetAvailableTagRequest",
    "AvailableTag"
]