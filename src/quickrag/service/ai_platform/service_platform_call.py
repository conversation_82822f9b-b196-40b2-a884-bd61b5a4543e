import hashlib
import json
import re

from datetime import datetime
from typing import Async<PERSON><PERSON>ator

import httpx
from rocket_core import Conf<PERSON>anager
from rocket_util import BizException

from quickrag.core.server_conf import AIPlatformConf
from quickrag.schema import AITaskRequest, AITaskResponse
from quickrag.service.base import BaseService


class AIPlatformCallService(BaseService):
    SSE_PATTERN = re.compile(r"^\s*data: (.*?)\s*\n\n$")

    @classmethod
    async def call(cls, request: AITaskRequest) -> AsyncGenerator[AITaskResponse, None]:
        conf = ConfManager.read_sc_conf(AIPlatformConf)
        async with httpx.AsyncClient(timeout=conf.timeout, verify=False) as client:
            async with client.stream("POST", conf.url, json=request.model_dump(by_alias=True)) as response:
                async for chunk in response.aiter_text():
                    yield AITaskResponse(cls.extract_json_from_sse(chunk))

    @classmethod
    def generate_timestamp(cls):
        # 获取当前时间，并格式化为指定字符串
        return datetime.now().strftime("%Y%m%d%H%M%S")

    @classmethod
    def generate_token(
        cls,
        app_id: str,
        timestamp: str,
        team_id: str,
        sk: str,
        conversation_id: str,
        content: str,
        variables: dict | None = None
    ):
        """
        根据指定规则生成MD5令牌

        参数:
            app_id (str): 应用ID
            timestamp (str): 时间戳
            team_id (str): 团队ID
            sk (str): 密钥
            conversation_id (str): 对话ID
            content (str): 内容
            variable (dict | None): 变量字典. 默认None表示无内容

        返回:
            str: 生成的MD5令牌
        """
        # 处理variables参数，默认为空字典
        if variables is None:
            variables = {}

        # 1. 处理Variable转字符串
        if not variables:
            variable_str = ""
        else:
            # 按key的ASCII码排序
            sorted_keys = sorted(variables.keys())
            # 拼接成key=value&key1=value1形式
            variable_parts = [f"{key}={variables[key]}" for key in sorted_keys]
            variable_str = "&".join(variable_parts)

        # 2. 按照规则拼接字符串
        concatenated = (
            f"{app_id}"
            f"{timestamp}"
            f"{team_id}"
            f"{sk}"
            f"{team_id}"
            f"{conversation_id}"
            f"{content}"
            f"{variable_str}"
        )

        # 3. 计算MD5值
        md5_hash = hashlib.md5()
        md5_hash.update(concatenated.encode('utf-8'))
        token = md5_hash.hexdigest()

        return token

    @classmethod
    def extract_json_from_sse(cls, line: str) -> dict | None:
        """
        匹配规则：以"data: "开头，以"\n\n"结尾，捕获中间所有字符（非贪婪匹配）
        """

        # 1. 正则匹配 SSE 格式，提取中间的内容部分（xxx）
        matched = cls.SSE_PATTERN.match(line, re.DOTALL)

        if not matched:
            raise BizException("非法的sse格式数据")

        # 2. 提取到的原始内容（可能是JSON字符串）
        json_str_candidate = matched.group(1)

        # 3. 验证并解析JSON
        try:
            # 处理可能的转义字符（如接口返回的\"需转为"）
            return json.loads(json_str_candidate)
        except json.JSONDecodeError as e:
            raise BizException(f"非法的sse格式数据，{str(e)}")