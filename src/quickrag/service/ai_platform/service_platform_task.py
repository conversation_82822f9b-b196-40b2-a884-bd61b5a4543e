import logging
import httpx
from httpx import TimeoutException, ConnectTimeout

from rocket_core import ConfManager
from rocket_util import BizException

from quickrag.core.server_conf import ToolWrapperServiceConf
from quickrag.schema import CreateTaskResponse, CreateTaskRequest, Response, SUCCESS_CODE
from quickrag.service.base import BaseService


class AIPlatformTaskService(BaseService):
    @classmethod
    async def create_ai_task(cls, request: CreateTaskRequest) -> CreateTaskResponse:
        conf = ConfManager.read_sc_conf(ToolWrapperServiceConf)
        async with httpx.AsyncClient() as client:
            url = f"{conf.url}/task/create"
            try:
                response = await client.post(url, json=request.model_dump())
                # 非200状态码抛出异常
                response.raise_for_status()
                response = Response[CreateTaskResponse](**response.json())
                if response.code != SUCCESS_CODE:
                    raise BizException(response.msg)
                if not response.data:
                    raise BizException("调用-创建ai执行任务失败，返回任务信息为空！")
                return response.data
            except ConnectTimeout:
                logging.error("连接-创建ai执行任务超时！")
                raise BizException("连接-创建ai执行任务超时！")
            except TimeoutException:
                logging.error("调用-创建ai执行任务超时！")
                raise BizException("调用-调用ai执行任务超时！")
            except Exception as e:
                logging.error(f"调用-创建ai执行任务报错：{str(e)}")
                raise BizException(f"调用-创建ai执行任务报错：{str(e)}")
