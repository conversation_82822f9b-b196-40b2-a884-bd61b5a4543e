from typing import <PERSON><PERSON>, Optional, AsyncGenerator

from langchain_core.messages import (
    HumanMessage,
    AIMessage,
    BaseMessage
)
from langchain_core.prompts import (
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate,
    AIMessagePromptTemplate
)
from pydantic import SecretStr
from sqlalchemy.ext.asyncio import AsyncSession
from langchain_openai import ChatOpenAI

from qr_orm import const as orm_const

from rocket_util import (
    ChinaUnicomOpenServiceChatModel,
    BizException,
    common
)
from rocket_util.common import (
    truncate,
    num_tokens_from_string,
    iter_with_status,
    iter_with_status_and_rm_think_tag
)
from rocket_core.server_conf import ConfManager

from quickrag.core import (
    manager,
    OpenServiceConf,
    CacheKey
)
from quickrag.schema import (
    Message,
    Role,
    ChatModel,
    LLMHandler,
    GenerationMode,
    UserMessage,
    ExpertMessage,
    AssistantMessage,
    LlmDTO,
    QARobotInfo,
    IntentRobotInfo,
    CommonInfo,
    SystemMessage,
)
from quickrag.service.query import QueryLLMConfService
from quickrag.core.server_conf import DbCacheConf


class BaseServiceHelper(object):
    @classmethod
    def create_chat_model(cls, llm_conf: LlmDTO, llm_setting: dict):
        os_conf = manager.get_sub_conf(OpenServiceConf)
        if llm_conf.protocol == orm_const.LLM_PROTOCOL_OPENAI_CHAT:
            model = ChatOpenAI(
                openai_api_base=llm_conf.openai_api_base,
                openai_api_key=SecretStr(llm_conf.openai_api_key),
                model_name=llm_conf.name,
                **llm_setting,
            )
        elif llm_conf.protocol == orm_const.LLM_PROTOCOL_CHINAUNICOM_OPEN_SERVICE:
            model = ChinaUnicomOpenServiceChatModel(
                api_url=llm_conf.openai_api_base,
                app_id=os_conf.app_id,
                app_secret=os_conf.app_secret,
                app_req_key=llm_conf.extra["req_key"],
                model=llm_conf.name,
                role_reflect=llm_conf.extra["role_reflect"],
                extra_headers=llm_conf.extra["extra_headers"],
                **llm_setting,
            )
        else:
            raise BizException(f"不支持的大模型协议：{llm_conf.protocol}")
        return model

    @classmethod
    def load_chat_model(cls, llm_conf: LlmDTO, llm_setting: dict, source: str, robot_id: int) -> ChatModel:
        cache_conf = ConfManager.read_sc_conf(DbCacheConf)
        # 如果未开启缓存，直接创建并返回
        if not cache_conf.use_cache:
            return cls.create_chat_model(llm_conf, llm_setting)

        model: ChatModel
        qa_robot_cache: QARobotInfo = manager.get_cache(CacheKey.QA_ROBOT)
        intent_robot_cache: IntentRobotInfo = manager.get_cache(CacheKey.INTENT_ROBOT)
        common_cache: CommonInfo = manager.get_cache(CacheKey.COMMON)

        if source == "qa":
            model = qa_robot_cache.models.get(robot_id, None)
            if not model:
                model = cls.create_chat_model(llm_conf, llm_setting)
                qa_robot_cache.models[robot_id] = model
        elif source == "intent":
            model = intent_robot_cache.models.get(robot_id, None)
            if not model:
                model = cls.create_chat_model(llm_conf, llm_setting)
                intent_robot_cache.models[robot_id] = model
        else:
            model = common_cache.model
            if not model:
                model = cls.create_chat_model(llm_conf, llm_setting)
                common_cache.model = model
        return model

    @classmethod
    def prune_message_list(
        cls, msg_list: list[Message], max_length: int
    ) -> Tuple[int, list[Message]]:
        def count():
            nonlocal msg_list
            total = 0
            for m in msg_list:
                total += num_tokens_from_string(m.content)
            return total

        c = count()
        if c < max_length:
            return c, msg_list

        pruned_msg_list = [m for m in msg_list if m.role == Role.SYSTEM]

        # 尝试加最后一条消息（提问消息）
        if len(pruned_msg_list) > 1:
            pruned_msg_list.append(msg_list[-1])

        msg_list = pruned_msg_list
        c = count()
        if c < max_length:
            return c, msg_list

        # 如果还是超，如果系统消息占比大于80%,剪裁系统消息
        system_count = num_tokens_from_string(msg_list[0].content)
        last_count = num_tokens_from_string(msg_list[-1].content)
        if system_count / (system_count + last_count) > 0.8:
            m = msg_list[0].content
            m = truncate(m, max_length - last_count)
            msg_list[0].content = m
            return max_length, msg_list

        # 否则剪裁最后一条消息
        m = msg_list[-1].content
        m = truncate(m, max_length - system_count)
        msg_list[-1].content = m
        return max_length, msg_list

    @classmethod
    def print_messages(cls, messages):
        for i, message in enumerate(messages):
            if isinstance(message, HumanMessage):
                print(f"<User>: {message.content}")
            elif isinstance(message, AIMessage):
                print(f"<Assistant>: {message.content}")
            elif isinstance(message, SystemMessage):
                print(f"<System>: {message.content}")

    @classmethod
    def add_no_think_tag(cls, msg_list: list[Message]) -> list[Message]:
        res = [msg for msg in msg_list[:-1]]
        new = msg_list[-1].model_copy()
        new.content += " /no_think"
        res.append(new)
        return res

    @classmethod
    async def create_llm_handler(
        cls, db: AsyncSession, chat_llm_id: int, chat_llm_setting: dict, source: str, robot_id: int
    ) -> LLMHandler:
        service = QueryLLMConfService()
        llm_conf = await service.query(db, chat_llm_id)

        handler = LLMHandler()
        handler.chat_model_conf = llm_conf
        handler.chat_model = cls.load_chat_model(handler.chat_model_conf, chat_llm_setting, source, robot_id)

        return handler

    @classmethod
    def is_a_questioner_role(cls, role: Role) -> bool:
        return role in (Role.USER, Role.EXPERT)

    @classmethod
    def convert_to_langchain_messages(cls, msg_list: list[Message]) -> list[BaseMessage]:
        # 创建系统消息模板
        system_template = "{content}"
        system_prompt = SystemMessagePromptTemplate.from_template(system_template)

        # 创建用户消息模板
        user_template = "{content}"
        user_prompt = HumanMessagePromptTemplate.from_template(user_template)

        # 创建AI消息模板
        ai_template = "{content}"
        ai_prompt = AIMessagePromptTemplate.from_template(ai_template)

        langchain_messages = []
        for idx, msg in enumerate(msg_list):
            if msg.role == Role.SYSTEM:
                langchain_messages.extend(
                    system_prompt.format_messages(content=msg.content)
                )
            elif cls.is_a_questioner_role(msg.role):
                content = msg.content
                if idx == len(msg_list) - 1:
                    content = f"【最新消息】{content}"
                langchain_messages.extend(
                    user_prompt.format_messages(content=content)
                )
            else:
                langchain_messages.extend(
                    ai_prompt.format_messages(content=msg.content)
                )
        return langchain_messages

    @classmethod
    def count_token_num(cls, msg_list: list[Message]):
        total = 0
        for msg in msg_list:
            total += common.num_tokens_from_string(msg.content)
        return total

    @classmethod
    def adapt_thinking_mode(cls, msg_list: list[Message], robot_chat_setting, chat_model_conf):
        """
        根据配置，适配思考模式
        """
        iter_func = iter_with_status

        # 当模型为推理模型，需要软关闭推理
        if chat_model_conf.with_thinking and not robot_chat_setting.enable_thinking:
            msg_list = cls.add_no_think_tag(msg_list)
            iter_func = iter_with_status_and_rm_think_tag

        return msg_list, iter_func

    @classmethod
    def create_message(
        cls,
        role: Role,
        content: str,
        epoch: int,
        message_id: Optional[str] = None,
        generation_mode: Optional[GenerationMode] = None,
        consumed_time: Optional[int] = None,
        **kwargs: dict, # noqa
    ) -> Message:
        if role == Role.USER:
            # 构建输入的消息
            msg = UserMessage()
        elif role == Role.EXPERT:
            msg = ExpertMessage()
        elif role == Role.SYSTEM:
            msg = SystemMessage()
        else:
            msg = AssistantMessage()
            msg.generation_mode = generation_mode
            msg.consumed_time = consumed_time

        msg.message_id = message_id if message_id else common.generate_uuid()
        msg.role = role
        msg.created_at = common.get_time()
        msg.content = content
        msg.epoch = epoch
        return msg

    @classmethod
    async def stream(
        cls, messages: list[Message], llm_handler: LLMHandler
    ) -> AsyncGenerator[str, None]:
        langchain_messages = cls.convert_to_langchain_messages(messages)
        # 直接使用 langchain_messages 而不是组合 chat_prompt
        async for chunk in llm_handler.chat_model.astream(langchain_messages):
            yield chunk.content
