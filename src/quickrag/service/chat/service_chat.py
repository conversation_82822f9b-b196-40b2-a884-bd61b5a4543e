from abc import ABC, abstractmethod
import asyncio
import logging
from asyncio import Queue
from typing import Optional, AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from qr_orm.orm import Conversation

from rocket_util import BizException, Timer
from quickrag.schema import ChatRequest, Block, Code
from quickrag.core import manager, db_util
from quickrag.service.base import BaseService, BaseServiceHelper
from quickrag.service.query import QueryAreaRobotRelationService


class BaseChatService(BaseService, ABC):
    def __init__(self, request: ChatRequest, db: Optional[AsyncSession] = None):
        # 请求
        self._request: ChatRequest = request
        # 数据库
        self._db: AsyncSession = db

        # 会话信息
        self._conversation: Optional[Conversation] = None

        # 是否触发风控的标识
        self._risk_flag: bool = False

    async def chat(self) -> AsyncGenerator[str, None]:
        chat_timer = Timer()
        b = Block()
        b.code = Code.ERROR
        async with db_util.AsyncSessionLocal() as db:
            self._db = db
            try:
                async for c in self._chat():
                    yield c
                if self._risk_flag or manager.is_test_account(self._request.account):
                    await db.rollback()
                else:
                    await db.commit()
            except BizException as e:
                logging.exception(str(e), exc_info=True)
                await db.rollback()
                b.error = e.message
                yield f"data: {b.model_dump_json()}\n\n"
            except Exception as e:
                logging.exception(str(e), exc_info=True)
                await db.rollback()
                b.error = str(e)
                yield f"data: {b.model_dump_json()}\n\n"
        logging.info(chat_timer.tik_with_desc("【Chat】总计"))

    @abstractmethod
    def _init_services(self):
        """初始化服务"""
        pass

    async def _chat(self) -> AsyncGenerator[str, None]:
        if not BaseServiceHelper.is_a_questioner_role(self._request.role):
            raise BizException("提问角色错误，只能为用户或专家！")

        logging.info(f"Query:{self._request.query}")
        logging.info(f"UseMode:{self._request.mode}")

        # 加载 or 创建会话
        a_new_c = await self._load_conversation()

        if a_new_c:
            # 新会话，通过区域获取相关机器人
            query_service = QueryAreaRobotRelationService()
            area_robot_conf = await query_service.query(
                self._db, self._request.province_code, self._request.city_code
            )
            # 更新会话的机器人id
            self._conversation.qa_robot_id = area_robot_conf.qa_robot_id
            self._conversation.intent_robot_id = area_robot_conf.intent_robot_id

        self._init_services()

        async for block in self._generate():
            yield f"data: {block.model_dump_json()}\n\n"

    async def _load_conversation(self) -> bool:
        # 查询会话
        stmt = select(Conversation).where(Conversation.biz_id == self._request.group_id)
        conversation = await self._db.scalar(stmt)
        if not conversation:
            # 创建会话
            self._conversation = Conversation()
            self._conversation.cid = manager.next_id()
            self._conversation.biz_id = self._request.group_id
            self._conversation.user_id = self._request.account
            self._conversation.name = ""
            self._conversation.qa_messages = []
            self._conversation.qa_reference = {}
            self._conversation.intent_messages = []
            self._conversation.epoch = 1
            self._conversation.ctx_intent_info_id = 0
            self._db.add(self._conversation)
            return True
        else:
            self._conversation = conversation
            self._conversation.epoch += 1
            return False

    @abstractmethod
    def _create_task_list(self, queue: Queue) -> list: # noqa
        return []

    async def _generate(self) -> AsyncGenerator[Block, None]:
        block_queue = asyncio.Queue()

        task_list = self._create_task_list(block_queue)

        # 启动额外任务监控异常
        async def wait_all_tasks(queue: asyncio.Queue, tasks):
            try:
                await asyncio.gather(*tasks)
                # 任务结束，发送结束信号
                await queue.put(None)
            except Exception as e:
                for task in tasks:
                    task.cancel()
                # 抛出异常
                await queue.put(e)

        asyncio.create_task(wait_all_tasks(block_queue, task_list))

        # 消费 block queue
        while True:
            item = await block_queue.get()
            if item is None:
                # 正常结束
                break
            elif isinstance(item, Exception):
                # 抛出异常
                raise item
            else:
                yield item

    def on_risk_triggered(self):
        self._risk_flag = True
