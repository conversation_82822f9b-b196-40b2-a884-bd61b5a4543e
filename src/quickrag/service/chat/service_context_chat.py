from asyncio import Queue
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession

from quickrag.schema import <PERSON><PERSON><PERSON><PERSON><PERSON>
from quickrag.service.chat.service_chat import BaseChatService
from quickrag.service.rag import ContextRagService


class ContextChatService(BaseChatService):
    def __init__(self, request: ChatRequest, db: Optional[AsyncSession] = None):
        super(ContextChatService, self).__init__(request, db)
        self._ctx_rag_service: Optional[ContextRagService] = None

    def _init_services(self):
        pass

    def _create_task_list(self, queue: Queue) -> list:
        pass