import logging
import httpx
from httpx import TimeoutException

from pydantic import BaseModel, Field

from rocket_core.server_conf import ConfManager
from quickrag.core.server_conf import AIAuditConf
from quickrag.service.base import BaseService


class AIAuditData(BaseModel):
    code: int = Field(default=200, description="返回码")
    msg: str = Field(default="", description="返回信息")


class AIAuditResponse(BaseModel):
    code: int = Field(default=200, description="返回码")
    msg: str = Field(default="", description="返回信息")
    data: AIAuditData | None = Field(default=None, description="返回数据")


class AIAuditService(BaseService):

    @classmethod
    async def audit(cls, content: str, email: str) -> bool:
        """调用AI网关进行审计，为不影响流程，超时时跳过"""
        conf: AIAuditConf = ConfManager.read_sc_conf(AIAuditConf)

        async with httpx.AsyncClient(timeout=conf.timeout) as client:
            payload = {
                "keyword": content,
                "userType": conf.user_type,
                "user": email,
                "agentId": conf.agent_id
            }
            try:
                response = await client.post(url=conf.url, json=payload)
                # 非200状态码抛出异常
                response.raise_for_status()
                response = AIAuditResponse(**response.json())
                if response.code != 200:
                    return False
                if not response.data:
                    return False
                if response.data.code != 200:
                    if response.data.msg:
                        logging.info(f"AI安全网关审计未通过，原因为：<{response.data.msg}>")
                    return False
                return True

            except TimeoutException:
                logging.error("调用AI安全网关超时！")
                return True
            except Exception as e:
                logging.error(f"调用AI安全网关报错：{str(e)}")
                return True
