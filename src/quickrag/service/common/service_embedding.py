import asyncio
from typing import <PERSON><PERSON>

from quickrag.core import manager
from quickrag.service.base import BaseService


class EmbeddingService(BaseService):

    @classmethod
    async def encode(cls, text):
        event_loop = asyncio.get_event_loop()
        res = await event_loop.run_in_executor(
            None, manager.embedding_model.encode, text
        )
        return res.tolist()

    @classmethod
    async def encode_query_and_docs(
        cls, query: str, docs: list[str]
    ) -> Tuple[list[float], list[list[float]]]:
        """对 query 和 docs 分别进行向量编码。

        Args:
            query (str): 查询文本。
            docs (List[str]): 示例文本列表。

        Returns:
            Tuple[List[float], List[List[float]]]: 包含 query 的向量和所有 example 向量的元组。
        """
        event_loop = asyncio.get_event_loop()

        query_embedding, example_embeddings = await asyncio.gather(
            event_loop.run_in_executor(None, manager.embedding_model.encode, query),
            event_loop.run_in_executor(None, manager.embedding_model.encode, docs)
        )
        return query_embedding.tolist(), [vec.tolist() for vec in example_embeddings]
