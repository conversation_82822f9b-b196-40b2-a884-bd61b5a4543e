from quickrag.schema import KeywordDTO, TagKeywordInfo
from quickrag.core import manager, <PERSON><PERSON><PERSON>ey
from quickrag.service.base import BaseService


class KeywordService(BaseService):
    def __init__(self):
        pass

    @classmethod
    def match_keyword_list(cls, query: str, province_code: str, city_code: str) -> list[KeywordDTO]:
        if not query or not province_code or not city_code:
            return []
        uppercase_query = query.upper()

        ret = []
        tag_kw_cache: TagKeywordInfo = manager.get_cache(CacheKey.TAG_KEYWORD)
        kw_map = tag_kw_cache.keyword_map

        for k in kw_map.global_:
            kw = kw_map.global_[k]
            if not kw.case_sensitive:
                if kw.keyword.upper() in uppercase_query:
                    ret.append(kw)
            else:
                if kw.keyword in query:
                    ret.append(kw)

        if province_code in kw_map.province:
            for k in kw_map.province[province_code]:
                kw = kw_map.province[province_code][k]
                if not kw.case_sensitive:
                    if kw.keyword.upper() in uppercase_query:
                        ret.append(kw)
                else:
                    if kw.keyword in query:
                        ret.append(kw)

        if city_code in kw_map.city:
            for k in kw_map.city[city_code]:
                kw = kw_map.city[city_code][k]
                if not kw.case_sensitive:
                    if kw.keyword.upper() in uppercase_query:
                        ret.append(kw)
                else:
                    if kw.keyword in query:
                        ret.append(kw)
        return ret

    @classmethod
    def filter_valid_tag_id(cls, province_code: str, city_code: str, tag_id_list: list[int]) -> set[int]:
        if not tag_id_list:
            return set()
        tag_kw_info: TagKeywordInfo = manager.get_cache(CacheKey.TAG_KEYWORD)
        tag_map = tag_kw_info.tag_map
        ret = set()
        # 过滤有效的tag_id
        for tag_id in tag_id_list:
            if tag_id in tag_map.global_:
                ret.add(tag_id)
            if province_code in tag_map.province and tag_id in tag_map.province[province_code]:
                ret.add(tag_id)
            if city_code in tag_map.city and tag_id in tag_map.city[city_code]:
                ret.add(tag_id)
        return ret
