from abc import ABC, abstractmethod
from typing import TypeVar, Generic

from sqlalchemy.ext.asyncio import AsyncSession

from quickrag.schema import CacheInfo
from quickrag.service.base import BaseService


Info = TypeVar("Info", bound=CacheInfo)


class LoadService(BaseService, ABC, Generic[Info]):
    @classmethod
    @abstractmethod
    async def load(cls, db: AsyncSession, *args, **kwargs) -> Info:
        pass

