from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from quickrag.schema import AreaInfo, AreaDTO
from qr_orm.const import STATUS_VALID, SCOPE_GLOBAL, SCOPE_PROVINCE, SCOPE_CITY
from qr_orm.orm import AreaInfo as AreaInfoDO

from quickrag.service.load_cache.service_load import LoadService


class LoadAreaInfoService(LoadService):
    @classmethod
    async def load(cls, db: AsyncSession, *args, **kwargs) -> AreaInfo:
        stmt = select(AreaInfoDO).where(AreaInfoDO.status == STATUS_VALID).execution_options(readonly=True)
        result = await db.scalars(stmt)
        records = result.all()

        global_area: AreaDTO | None = None
        province_map: dict[str, AreaDTO] = {}
        city_map: dict[str, AreaDTO] = {}
        cb_province_map: dict[str, AreaDTO] = {}
        cb_city_map: dict[str, AreaDTO] = {}

        record: AreaInfoDO
        for record in records:
            area = AreaDTO(
                area_id=record.area_id,
                area_name=record.area_name,
                scope=record.scope, # type: ignore
                province_code=record.province_code,
                city_code=record.city_code,
                cb_province_code=record.cb_province_code,
                cb_city_code=record.cb_city_code,
            )

            if record.scope == SCOPE_GLOBAL:
                global_area = area
            elif record.scope == SCOPE_PROVINCE:
                province_map[record.province_code] = area
                cb_province_map[record.cb_province_code] = area
            elif record.scope == SCOPE_CITY:
                city_map[record.city_code] = area
                cb_city_map[record.cb_city_code] = area

        g_count = 1 if global_area is not None else 0
        p_count = len(province_map)
        c_count = len(city_map)
        count = g_count + p_count + c_count

        count_desc = f"加载地域信息成功：全国{g_count}条、省分{p_count}条、地市{c_count}条"
        return AreaInfo(
            count=count,
            count_desc=count_desc,
            global_=global_area,
            province=province_map,
            city=city_map,
            cb_province=cb_province_map,
            cb_city=cb_city_map,
        )


__all__ = [
    "LoadAreaInfoService"
]
