from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from qr_orm import const as orm_const
from qr_orm.orm import AreaRobot
from quickrag.schema.schema_base import Scope
from quickrag.schema import AreaRobotRelationInfo, AreaRobotRelationDTO
from quickrag.service.load_cache import LoadService


class LoadAreaRobotRelationService(LoadService):
    @classmethod
    async def load(cls, db: AsyncSession, *args, **kwargs) -> AreaRobotRelationInfo:
        """将数据库中的地域配置全部加载到内存"""
        stmt = select(AreaRobot).where(AreaRobot.status == orm_const.STATUS_VALID)

        result = await db.scalars(stmt)
        records = result.all()

        global_ = None
        province = dict()
        city = dict()
        g_count, p_count, c_count, count = 0, 0, 0, 0
        for record in records:
            conf = AreaRobotRelationDTO(
                area_conf_id=record.area_robot_id,
                city_code=record.city_code,
                province_code=record.province_code,
                intent_robot_id=record.intent_robot_id,
                qa_robot_id=record.qa_robot_id,
                scope=record.scope,  # type: ignore
            )

            if record.scope == Scope.GLOBAL:
                global_ = conf
                g_count += 1
            elif record.scope == Scope.PROVINCE:
                province[record.province_code] = conf
                p_count += 1
            elif record.scope == Scope.CITY:
                city[record.city_code] = conf
                c_count += 1

        count = g_count + p_count + c_count
        count_desc = f"加载地域机器人关系配置成功：全国{g_count}、省分{p_count}、地市{c_count}"

        return AreaRobotRelationInfo(
            count=count,
            count_desc=count_desc,
            global_=global_,
            province=province,
            city=city
        )


__all__ = [
    "LoadAreaRobotRelationService"
]
