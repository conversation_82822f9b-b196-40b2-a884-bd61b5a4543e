from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from qr_orm.orm import CommonConf
from quickrag.schema import CommonInfo, CommonConfDTO
from quickrag.service.load_cache import LoadService


class LoadCommonConfService(LoadService):
    @classmethod
    async def load(cls, db: AsyncSession, *args, **kwargs) -> CommonInfo:
        stmt = select(CommonConf).limit(1)
        common_conf: CommonConf = await db.scalar(stmt)

        dto = CommonConfDTO(
            id=common_conf.id,
            default_llm_id=common_conf.default_llm_id
        )
        return CommonInfo(
            count=1,
            count_desc=f"加载通用配置信息成功：1条数据",
            common=dto,
            model=None
        )


__all__ = [
    "LoadCommonConfService"
]
