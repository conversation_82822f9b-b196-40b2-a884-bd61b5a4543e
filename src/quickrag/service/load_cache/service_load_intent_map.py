from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from qr_orm.orm import Intent, IntentCategory
from qr_orm.const import STATUS_VALID
from quickrag.schema import IntentDTO, IntentCategoryDTO, IntentInfo
from quickrag.schema.schema_base import Scope, IntentLevel
from quickrag.service.load_cache import LoadService


class LoadIntentMapService(LoadService):
    @classmethod
    async def load(cls, db: AsyncSession, *args, **kwargs) -> IntentInfo:
        count, g_count, p_count, l1_count, l2_count, c_count = 0, 0, 0, 0, 0, 0

        stmt = select(IntentCategory).where(IntentCategory.status == STATUS_VALID)
        result = await db.scalars(stmt)
        records = result.all()

        record: IntentCategory

        global_category = dict()
        province_category = dict()
        city_category = dict()

        for record in records:
            dto = IntentCategoryDTO(
                id=record.category_id,
                name=record.category_name,
                desc=record.category_desc,
                scope=record.scope, # type: ignore
                province_code=record.province_code,
                city_code=record.city_code,
                tenant_id=record.tenant_id,
                target_role=record.target_role,
            )

            if record.scope == Scope.GLOBAL:
                global_category[dto.id] = dto
                g_count += 1
            elif record.scope == Scope.PROVINCE:
                province_category.setdefault(record.province_code, dict())[dto.id] = dto
                p_count += 1
            elif record.scope == Scope.CITY:
                city_category.setdefault(record.city_code, dict())[dto.id] = dto
                c_count += 1

        stmt = select(Intent).where(Intent.status == STATUS_VALID)
        result = await db.scalars(stmt)
        records = result.all()

        record: Intent
        level_1_dict = dict()
        level_2_dict = dict()
        for record in records:
            dto = IntentDTO(
                id=record.intent_id,
                name=record.intent_name,
                desc=record.intent_desc,
                level=record.intent_level,
                parent_id=record.parent_intent_id,
                category_id=record.category_id,
            )

            if record.intent_level == IntentLevel.LEVEL_1:
                level_1_dict[dto.id] = dto
                l1_count += 1
            elif record.intent_level == IntentLevel.LEVEL_2:
                level_2_dict[dto.id] = dto
                l2_count += 1
        count = g_count + p_count + c_count + l1_count + l2_count
        count_desc = f"加载意图类别等数据成功：全国意图类{g_count}条、省分意图类{p_count}条、地市意图类{c_count}条、一级意图{l1_count}条、二级意图{l2_count}条"
        return IntentInfo(
            count=count,
            count_desc=count_desc,
            global_category=global_category,
            province_category=province_category,
            city_category=city_category,
            level_1_dict=level_1_dict,
            level_2_dict=level_2_dict,
        )


__all__ = [
    "LoadIntentMapService",
]