from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from qr_orm.orm import IntentRobot
from qr_orm.const import STATUS_VALID
from quickrag.schema import IntentRobotDTO, IntentRobotExtraSetting, IntentRobotInfo
from quickrag.service.load_cache import LoadService


class LoadIntentRobotConfService(LoadService):
    @classmethod
    async def load(cls, db: AsyncSession, *args, **kwargs) -> IntentRobotInfo:
        stmt = select(IntentRobot).where(IntentRobot.status == STATUS_VALID)
        result = await db.scalars(stmt)
        records = result.all()

        record: IntentRobotDTO
        robots = {}
        for record in records:
            chat_setting = IntentRobotExtraSetting(**record.chat_setting)
            conf = IntentRobotDTO(
                id=record.intent_robot_id,
                name=record.robot_name,
                desc=record.robot_desc,
                chat_llm_id=record.chat_llm_id,
                vision_llm_id=record.vision_llm_id,
                chat_llm_setting=record.chat_llm_setting,
                vision_llm_setting=record.vision_llm_setting,

                chat_setting=chat_setting,
                vision_setting=record.vision_setting,

                similarity_threshold=record.similarity_threshold,
                vector_similarity_weight=record.vector_similarity_weight,

                top_n=record.top_n,
                top_k=record.top_k,
                tenant_id=record.tenant_id,
                category_ids=record.category_ids,
            )
            robots[conf.id] = conf
        return IntentRobotInfo(
            count=len(robots),
            count_desc=f"加载Intent机器人配置成功：机器人配置{len( robots)}条",
            robots=robots,
            models={}
        )


__all__ = [
    "LoadIntentRobotConfService"
]