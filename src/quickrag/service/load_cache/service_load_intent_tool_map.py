from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from qr_orm.orm import Tool, ToolScene, IntentSceneMap
from qr_orm.const import STATUS_VALID
from quickrag.schema import ToolDTO, ToolSceneDTO, IntentSceneRelationInfo
from quickrag.service.load_cache import LoadService


class LoadIntentToolMapService(LoadService):
    @classmethod
    async def load(cls, db: AsyncSession, *args, **kwargs) -> IntentSceneRelationInfo:
        t_count, s_count, r_count = 0, 0, 0
        stmt = select(Tool).where(Tool.status == STATUS_VALID)
        result = await db.scalars(stmt)
        records = result.all()

        record: Tool
        tool_dict = dict()
        for record in records:
            dto = ToolDTO(
                id=record.tool_id,
                name=record.tool_name,
                desc=record.tool_desc,
                type=record.tool_type,
            )
            tool_dict[dto.id] = dto
            t_count += 1

        stmt = select(ToolScene).where(ToolScene.status == STATUS_VALID)
        result = await db.scalars(stmt)
        records = result.all()

        scene_record: ToolScene
        scene_dict = dict()
        for scene_record in records:
            dto = ToolSceneDTO(
                id=scene_record.tool_scene_id,
                name=scene_record.scene_name,
                desc=scene_record.scene_desc,
                tool_id=scene_record.tool_id,
                type=scene_record.scene_type, # type: ignore
                code=scene_record.scene_code
            )
            scene_dict[dto.id] = dto
            s_count += 1


        stmt = select(IntentSceneMap).where(IntentSceneMap.status == STATUS_VALID)
        result = await db.scalars(stmt)
        records = result.all()

        reflect_record: IntentSceneMap

        count = 0
        map_ = dict()
        for reflect_record in records:
            count += 1
            map_[reflect_record.intent_id] = reflect_record.scene_id
            r_count += 1

        count = t_count + s_count + r_count
        count_desc = f"加载意图-场景关系信息成功，工具数量：{t_count}，场景数量：{s_count}，映射关系数量：{r_count}"
        return IntentSceneRelationInfo(
            count=count,
            count_desc=count_desc,
            tools=tool_dict,
            scenes=scene_dict,
            intent2scene=map_
        )


__all__ = ["LoadIntentToolMapService"]
