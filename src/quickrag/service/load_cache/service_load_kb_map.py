from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_

from quickrag.schema import KbDTO, KbInfo, Scope, RefType
from qr_orm.orm import KnowledgeBase
from qr_orm.const import STATUS_VALID

from quickrag.service.load_cache import LoadService


class LoadKbMapService(LoadService):
    @classmethod
    async def load(cls, db: AsyncSession, *args, **kwargs) -> KbInfo:
        g_count, p_count, c_count, count = 0, 0, 0, 0
        stmt = select(KnowledgeBase).where(
            and_(
                KnowledgeBase.status == STATUS_VALID,
                KnowledgeBase.scope.in_([s.value for s in Scope]),
                KnowledgeBase.kb_type.in_([r.value for r in RefType])
            )
        ).execution_options(readonly=True)
        result = await db.scalars(stmt)
        records = result.all()

        global_kb = dict()
        province_kb = dict()
        city_kb = dict()

        record: KnowledgeBase
        count = 0
        for record in records:
            count += 1
            province_code = record.province_code
            city_code = record.city_code
            if record.scope == Scope.GLOBAL:
                province_code = "000000"
                city_code = "000000"
            elif record.scope == Scope.PROVINCE:
                city_code = province_code

            special_tag_ids = [] if not record.special_tag_ids else record.special_tag_ids

            kb_dto = KbDTO(
                id=record.id,
                name=record.name,
                desc=record.description,
                type=record.kb_type, # type: ignore
                scope=record.scope, # type: ignore
                province_code=province_code,
                city_code=city_code,
                tenant_id=record.tenant_id,
                domain=record.kb_domain,
                similarity_threshold=record.similarity_threshold,
                weight=record.weight,
                vector_similarity_weight=record.vector_similarity_weight,
                special_tag_ids=special_tag_ids
            )
            if record.scope == Scope.GLOBAL:
                global_kb[kb_dto.id] = kb_dto
                g_count += 1
            elif record.scope == Scope.PROVINCE:
                province_kb.setdefault(record.province_code, dict())[kb_dto.id] = kb_dto
                p_count += 1
            elif record.scope == Scope.CITY:
                city_kb.setdefault(record.city_code, dict())[kb_dto.id] = kb_dto
                c_count += 1
        count = g_count + p_count + c_count
        count_desc = f"加载知识库识配置成功，其中全国{g_count}个，省{p_count}个，地{c_count}个"
        return KbInfo(
            count=count,
            count_desc=count_desc,
            global_=global_kb,
            province=province_kb,
            city=city_kb
        )


__all__ = [
    "LoadKbMapService"
]
