from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from qr_orm.orm import LLM
from quickrag.schema import LlmInfo, LlmDTO
from qr_orm.const import STATUS_VALID

from quickrag.service.load_cache import LoadService


class LoadLLMConfService(LoadService):
    @classmethod
    async def load(cls, db: AsyncSession, *args, **kwargs) -> LlmInfo:
        stmt = select(LLM).where(LLM.status == STATUS_VALID)
        result = await db.scalars(stmt)
        records = result.all()

        record: LLM

        llms = dict()
        for record in records:
            llms[record.llm_id] = LlmDTO(
                id=record.llm_id,
                alias=record.alias,
                llm_type=record.llm_type, # type: ignore
                name=record.name,
                openai_api_base=record.openai_api_base,
                openai_api_key=record.openai_api_key,
                protocol=record.protocol, # type: ignore
                with_thinking=record.with_thinking,
                extra=record.extra
            )
        return LlmInfo(
            count=len(llms),
            count_desc=f"加载大模型配置成功：{len(llms)}条数据",
            llms=llms
        )


__all__ = [
    "LoadLLMConfService"
]
