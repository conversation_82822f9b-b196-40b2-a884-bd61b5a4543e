from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from qr_orm.orm import QARobot
from qr_orm.const import STATUS_VALID
from quickrag.schema import QARobotDTO, QAChatExtraSetting, QARobotInfo
from quickrag.service.load_cache import LoadService


class LoadQARobotConfService(LoadService):
    @classmethod
    async def load(cls, db: AsyncSession, *args, **kwargs) -> QARobotInfo:
        stmt = select(QARobot).where(QARobot.status == STATUS_VALID)
        result = await db.scalars(stmt)
        records = result.all()

        record: QARobot
        robots = {}
        for record in records:
            chat_setting = QAChatExtraSetting(**record.chat_setting)
            conf = QARobotDTO(
                id=record.robot_id,
                name=record.robot_name,
                desc=record.robot_desc,
                chat_llm_id=record.chat_llm_id,
                vision_llm_id=record.vision_llm_id,
                chat_llm_setting=record.chat_llm_setting,
                vision_llm_setting=record.vision_llm_setting,
                chat_setting=chat_setting,
                vision_setting=record.vision_setting,
                enable_llm_solo=record.enable_llm_solo,
                prologue=record.prologue,
                empty_response=record.empty_response,
                similarity_threshold=record.similarity_threshold,
                rerank_threshold=record.rerank_threshold,
                vector_similarity_weight=record.vector_similarity_weight,
                tp_n=record.top_n,
                top_k=record.top_k,
                do_refer=record.do_refer,
                tenant_id=record.tenant_id,
                kb_ids=record.kb_ids,
            )
            robots[conf.id] = conf
        return QARobotInfo(
            count=len(robots),
            count_desc=f"加载QA机器人配置成功：{len(robots)}条数据",
            robots=robots,
            models={}
        )


__all__ = [
    "LoadQARobotConfService"
]
