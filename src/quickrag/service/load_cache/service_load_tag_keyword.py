from sqlalchemy import and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

import qr_orm.const as orm_const
from qr_orm.orm import Tag, TagKeyword
from quickrag.schema import KeywordMapDTO, TagKeywordInfo, TagMapDTO, TagDTO, KeywordDTO
from qr_orm.const import SCOPE_GLOBAL, SCOPE_CITY, SCOPE_PROVINCE

from quickrag.service.load_cache import LoadService


class LoadTagKeywordService(LoadService):
    @classmethod
    async def load(cls, db: AsyncSession, *args, **kwargs) -> TagKeywordInfo:
        # 标签数量
        tag_g_count, tag_p_count, tag_c_count = 0, 0, 0
        # 关键词数量
        kw_g_count, kw_p_count, kw_c_count = 0, 0, 0

        stmt = select(Tag).where(Tag.status == orm_const.STATUS_VALID).execution_options(readonly=True)
        tag_list = list(await db.scalars(stmt))
        if not tag_list:
            return TagKeywordInfo(
                count=0,
                count_desc="警告：加载标签关键字成功：共加载0条数据",
                tag_map=TagMapDTO(),
                keyword_map=KeywordMapDTO()
            )

        # 生成有效的标签信息
        tag_id_list = []
        tag_map = TagMapDTO(global_=dict(), province=dict(), city=dict())

        for orm_tag in tag_list:
            tag_dto = TagDTO(
                tag_id=orm_tag.tag_id,
                tag_name=orm_tag.tag_name,
                tag_desc=orm_tag.tag_desc,
                scope=orm_tag.scope,
                province_code=orm_tag.province_code,
                city_code=orm_tag.city_code,
            )

            tag_id_list.append(orm_tag.tag_id)

            if orm_tag.scope == SCOPE_GLOBAL:
                tag_map.global_[orm_tag.tag_id] = tag_dto
                tag_g_count += 1
            elif orm_tag.scope == SCOPE_PROVINCE:
                tag_map.province.setdefault(orm_tag.province_code, dict())[orm_tag.tag_id] = tag_dto
                tag_p_count += 1
            elif orm_tag.scope == SCOPE_CITY:
                tag_map.city.setdefault(orm_tag.city_code, dict())[orm_tag.tag_id] = tag_dto
                tag_c_count += 1
            else:
                raise Exception(f"Scope非法值: {orm_tag.scope}")

        stmt = select(TagKeyword).where(
            and_(
                TagKeyword.tag_id.in_(tag_id_list),
                TagKeyword.status == orm_const.STATUS_VALID
            )
        )
        kw_list = list(await db.scalars(stmt))
        if not kw_list:
            return TagKeywordInfo(
                count=0,
                count_desc="警告：加载标签关键字成功：共加载0条数据",
                tag_map=tag_map,
                keyword_map=KeywordMapDTO()
            )

        orm_tag_dict = {tag.tag_id: tag for tag in tag_list}

        kw_map = KeywordMapDTO(global_=dict(), province=dict(), city=dict())
        for k in kw_list:
            orm_tag = orm_tag_dict[k.tag_id]
            kw = KeywordDTO(
                keyword_id=k.keyword_id,
                tag_id=k.tag_id,
                keyword=k.keyword,
                category=k.category,
                case_sensitive=k.case_sensitive,
            )

            if orm_tag.scope == SCOPE_GLOBAL:
                kw_map.global_[k.keyword] = kw
                kw_g_count += 1

            elif orm_tag.scope == SCOPE_PROVINCE:
                kw_map.province.setdefault(orm_tag.province_code, {})[k.keyword] = kw
                kw_p_count += 1

            elif orm_tag.scope == SCOPE_CITY:
                kw_map.city.setdefault(orm_tag.province_code, {})[k.keyword] = kw
                kw_c_count += 1

            else:
                raise Exception(f"非法作用域: {orm_tag.scope}")

        tag_count = tag_g_count + tag_p_count + tag_c_count
        kw_count = kw_g_count + kw_p_count + kw_c_count
        count = tag_count + kw_count

        count_desc = (
            f"加载标签-关键词成功，共计{count}条数据。"
            f"标签共计{tag_count}个："
            f"全国标签{tag_g_count}个，省分标签{tag_p_count}个，地市标签{tag_c_count}个。"
            f"关键词共计{kw_count}个："
            f"全国关键词{kw_g_count}个，省分关键词{kw_p_count}个，地市关键词{kw_c_count}个。"
        )
        return TagKeywordInfo(
            count=count,
            count_desc=count_desc,
            tag_map=tag_map,
            keyword_map=kw_map
        )


__all__ = [
    "LoadTagKeywordService"
]
