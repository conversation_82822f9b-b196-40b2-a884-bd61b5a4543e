from typing import Any
from abc import abstractmethod, ABC

from quickrag.service.base import BaseService


class QueryService(BaseService, ABC):
    @classmethod
    @abstractmethod
    async def query(cls, *args, **kwargs) -> Any:
        pass

    @classmethod
    @abstractmethod
    async def _query_from_cache(cls, *args, **kwargs) -> Any:
        pass

    @classmethod
    @abstractmethod
    async def _query_from_db(cls, *args, **kwargs) -> Any:
        pass
