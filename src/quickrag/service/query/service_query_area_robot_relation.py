from typing import Optional

from sqlalchemy import and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from rocket_util import BizException

from qr_orm import const as orm_const
from qr_orm.orm import AreaRobot

from rocket_core.server_conf import ConfManager
from quickrag.core.server_conf import DbCacheConf
from quickrag.core import manager, CacheKey
from quickrag.schema import AreaRobotRelationDTO, AreaRobotRelationInfo
from quickrag.service.query.service_query import QueryService


class QueryAreaRobotRelationService(QueryService):
    @classmethod
    async def query(cls, db: AsyncSession, province_code: str, city_code: str) -> AreaRobotRelationDTO:
        cache_conf = ConfManager.read_sc_conf(DbCacheConf)
        if cache_conf.use_cache:
            area_robot_relation = await cls._query_from_cache(province_code, city_code)
        else:
            area_robot_relation = await cls._query_from_db(db, province_code, city_code)

        if not area_robot_relation:
            raise BizException(f"没有匹配的地区机器人，省分编码：{province_code},地市编码：{city_code}")
        return area_robot_relation

    @classmethod
    async def _query_from_cache(cls, province_code: str, city_code: str) -> AreaRobotRelationDTO:
        cache: AreaRobotRelationInfo = manager.get_cache(CacheKey.AREA_ROBOT_RELATION)
        if city_code in cache.city:
            return cache.city[city_code]
        elif province_code in cache.province:
            return cache.province[province_code]
        return cache.global_

    @classmethod
    async def _query_from_db(cls, db: AsyncSession, province_code: str, city_code: str) -> Optional[AreaRobotRelationDTO]:
        stmt = (
            select(AreaRobot)
            .where(
                and_(
                    or_(
                        and_(AreaRobot.scope == orm_const.SCOPE_GLOBAL),
                        and_(
                            AreaRobot.province_code == province_code,
                            AreaRobot.scope == orm_const.SCOPE_PROVINCE,
                        ),
                        and_(
                            AreaRobot.province_code == province_code,
                            AreaRobot.city_code == city_code,
                            AreaRobot.scope == orm_const.SCOPE_CITY,
                        ),
                    ),
                    AreaRobot.status == orm_const.STATUS_VALID,
                )
            ).execution_options(readonly=True)
            .order_by(AreaRobot.scope.desc())
        )
        area_robot: AreaRobot = await db.scalar(stmt)
        if not area_robot:
            return None

        return AreaRobotRelationDTO(
            area_conf_id=area_robot.area_robot_id,
            scope=area_robot.scope, # type: ignore
            province_code=area_robot.province_code,
            city_code=area_robot.city_code,
            qa_robot_id=area_robot.qa_robot_id,
            intent_robot_id=area_robot.intent_robot_id
        )
