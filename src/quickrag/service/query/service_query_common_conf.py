from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from rocket_util import BizException

from rocket_core.server_conf import ConfManager
from quickrag.core.server_conf import DbCacheConf
from quickrag.core import manager, <PERSON><PERSON><PERSON><PERSON>
from quickrag.schema import CommonConfDTO, CommonInfo
from qr_orm.orm import CommonConf

from quickrag.service.query.service_query import QueryService


class QueryCommonConfService(QueryService):
    @classmethod
    async def query(cls, db: AsyncSession) -> CommonConfDTO:
        cache_conf = ConfManager.read_sc_conf(DbCacheConf)
        if cache_conf.use_cache:
            common_conf = await cls._query_from_cache()
        else:
            common_conf = await cls._query_from_db(db)

        if not common_conf:
            raise BizException(f"没有公共配置信息")
        return common_conf

    @classmethod
    async def _query_from_cache(cls) -> CommonConfDTO:
        cache: CommonInfo = manager.get_cache(CacheKey.COMMON)
        return cache.common

    @classmethod
    async def _query_from_db(cls, db: AsyncSession) -> Optional[CommonConfDTO]:
        common_stmt = select(CommonConf).limit(1).execution_options(readonly=True)
        common_conf = await db.scalar(common_stmt)
        if not common_conf:
            return None
        return CommonConfDTO(
            id=common_conf.id,
            default_llm_id=common_conf.default_llm_id,
            model=None
        )
