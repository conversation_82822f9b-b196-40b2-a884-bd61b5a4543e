from typing import Optional

from sqlalchemy import and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from rocket_util.exception import BizException
from qr_orm.orm import IntentCategory
from qr_orm import const as orm_const
from rocket_core.server_conf import ConfManager
from quickrag.core.server_conf import DbCacheConf
from quickrag.schema import IntentCategoryDTO, IntentInfo, Role
from quickrag.core import manager, <PERSON><PERSON><PERSON>ey
from quickrag.service.query.service_query import QueryService


class QueryIntentCategoryListService(QueryService):
    @classmethod
    async def query(
        cls, db: AsyncSession, role: Role, province_code: str, city_code: str,
        category_ids: list[int], tenant_id: str
    ) -> list[IntentCategoryDTO]:
        """查询意图类别列表入口方法"""
        cache_conf = ConfManager.read_sc_conf(DbCacheConf)
        if cache_conf.use_cache:
            category_list = await cls._query_from_cache(role, province_code, city_code, category_ids, tenant_id)
        else:
            category_list = await cls._query_from_db(db, role, province_code, city_code, category_ids, tenant_id)

        return category_list

    @classmethod
    async def _query_from_cache(
        cls, role: Role, province_code: str, city_code: str, category_ids: list[int], tenant_id: str
    ) -> list[IntentCategoryDTO]:
        """从缓存中查询意图类别列表，逻辑与数据库查询保持一致"""
        cache: IntentInfo = manager.get_cache(CacheKey.INTENT)
        candidate_list: list[IntentCategoryDTO] = []
        extra_category_ids: set[int] = set(category_ids) if category_ids else set()

        # 1. 处理目标角色列表（与数据库查询逻辑一致）
        target_role_list = []
        if role == Role.USER:
            target_role_list.append(Role.USER.value)
        elif role == Role.EXPERT:
            target_role_list.append(Role.USER.value)
            target_role_list.append(Role.EXPERT.value)
        else:
            raise BizException("角色错误")

        # 2. 收集全局范围的意图分类
        for category in cache.global_category.values():
            if category.tenant_id == tenant_id and category.target_role in target_role_list:
                candidate_list.append(category)

        # 3. 收集省份范围的意图分类
        if province_code in cache.province_category:
            for category in cache.province_category[province_code].values():
                if (
                    category.tenant_id == tenant_id and
                    category.scope == orm_const.SCOPE_PROVINCE and
                    category.target_role in target_role_list
                ):
                    candidate_list.append(category)

        # 4. 收集城市范围的意图分类
        if city_code in cache.city_category:
            for category in cache.city_category[city_code].values():
                if (
                    category.tenant_id == tenant_id and
                    category.scope == orm_const.SCOPE_CITY and  # 确保是市级范围
                    category.target_role in target_role_list
                ):
                    candidate_list.append(category)

        # 5. 收集额外指定ID的意图分类（不在上述范围内的）
        existing_ids = {category.id for category in candidate_list}
        for category_id in extra_category_ids:
            if category_id not in existing_ids:
                # 从所有可能的来源查找这个分类ID
                category = cls._find_category_in_map(cache, category_id)
                if category and category.tenant_id == tenant_id and category.target_role in target_role_list:
                    candidate_list.append(category)
                    existing_ids.add(category_id)

        return candidate_list

    @classmethod
    def _find_category_in_map(cls, intent_map: IntentInfo, category_id: int) -> Optional[IntentCategoryDTO]:
        """在intent_map中查找指定ID的意图分类"""
        # 检查所有省分
        for province_categories in intent_map.province_category.values():
            if category_id in province_categories:
                return province_categories[category_id]

        # 检查所有城市
        for city_categories in intent_map.city_category.values():
            if category_id in city_categories:
                return city_categories[category_id]

        return None

    @classmethod
    async def _query_from_db(
        cls, db: AsyncSession, role: Role, province_code: str, city_code: str, category_ids: list[int],
        tenant_id: str
    ) -> list[IntentCategoryDTO]:
        target_role_list = []
        if role == Role.USER:
            target_role_list.append(Role.USER.value)
        elif role == Role.EXPERT:
            target_role_list.append(Role.USER.value)
            target_role_list.append(Role.EXPERT.value)
        else:
            raise BizException("角色错误")

        if not category_ids:
            category_ids = []
        stmt = select(IntentCategory).where(
            and_(
                or_(
                    IntentCategory.scope == orm_const.SCOPE_GLOBAL,
                    and_(
                        IntentCategory.scope == orm_const.SCOPE_PROVINCE,
                        IntentCategory.province_code == province_code
                    ),
                    and_(
                        IntentCategory.scope == orm_const.SCOPE_CITY,
                        IntentCategory.province_code == province_code,
                        IntentCategory.city_code == city_code
                    ),
                    IntentCategory.category_id.in_(category_ids)
                ),
                IntentCategory.status == orm_const.STATUS_VALID,
                IntentCategory.tenant_id == tenant_id,
                IntentCategory.target_role.in_(target_role_list)
            )
        ).execution_options(readonly=True)
        orm_list =  list(await db.scalars(stmt))
        return cls._convert_to_dto_list(orm_list)

    @classmethod
    def _convert_to_dto_list(cls, orm_list: list[IntentCategory]) -> list[IntentCategoryDTO]:
        """将IntentCategory ORM对象列表转换为IntentCategoryDTO列表"""
        return [
            IntentCategoryDTO(
                id=orm_obj.category_id,
                name=orm_obj.category_name,
                desc=orm_obj.category_desc,
                scope=orm_obj.scope, # type: ignore
                province_code=orm_obj.province_code,
                city_code=orm_obj.city_code,
                target_role=orm_obj.target_role,
                tenant_id=orm_obj.tenant_id
            )
            for orm_obj in orm_list
        ]