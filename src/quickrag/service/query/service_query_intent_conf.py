from sqlalchemy import and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from quickrag.core import manager, <PERSON><PERSON><PERSON><PERSON>
from quickrag.core.server_conf import DbCacheConf
from quickrag.schema import IntentDTO, IntentInfo, IntentLevel
from qr_orm.orm import Intent as IntentDO
from qr_orm import const as orm_const
from rocket_core import ConfManager

from quickrag.service.query.service_query import QueryService


class QueryIntentConfService(QueryService):
    @classmethod
    async def query(cls, db: AsyncSession, id_list: list[int], level: IntentLevel) -> list[IntentDTO]:
        cache_conf = ConfManager.read_sc_conf(DbCacheConf)
        if cache_conf.use_cache:
            l = await cls._query_from_cache(id_list, level)
        else:
            l = await cls._query_from_db(db, id_list, level)

        return l

    @classmethod
    async def _query_from_cache(cls, id_list: list[int], level: IntentLevel) -> list[IntentDTO]:
        cache: IntentInfo
        cache = manager.get_cache(CacheKey.INTENT)

        if level == IntentLevel.LEVEL_2:
            intent_dict = cache.level_2_dict
        else:
            intent_dict = cache.level_1_dict

        ret = []
        for _id in id_list:
            intent = intent_dict.get(_id, None)
            if intent:
                ret.append(intent)

        return ret

    @classmethod
    async def _query_from_db(cls, db: AsyncSession, id_list: list[int], level: IntentLevel) -> list[IntentDTO]:
        stmt = select(IntentDO).where(
            and_(
                IntentDO.intent_id.in_(id_list),
                IntentDO.status == orm_const.STATUS_VALID,
                IntentDO.intent_level == level
            )
        )
        result = list(await db.scalars(stmt))
        if not result:
            return []
        record: IntentDO
        ret = []
        for record in result:
            dto = IntentDTO(
                id=record.intent_id,
                name=record.intent_name,
                desc=record.intent_desc,
                level=record.intent_level, # type: ignore
                parent_id=record.parent_intent_id,
                category_id=record.category_id,
            )
            ret.append(dto)
        return ret
