from typing import Optional

from qr_orm.const import STATUS_VALID
from qr_orm.orm_intent_robot import IntentRobot
from rocket_core import ConfManager
from rocket_util import BizException
from sqlalchemy import and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from quickrag.core import manager, <PERSON><PERSON><PERSON><PERSON>
from quickrag.core.server_conf import DbCacheConf
from quickrag.schema import IntentRobotDTO, IntentRobotExtraSetting, IntentRobotInfo
from quickrag.service.query.service_query import QueryService


class QueryIntentRobotConfService(QueryService):
    @classmethod
    async def query(cls, db: AsyncSession, robot_id: int) -> IntentRobotDTO:
        cache_conf = ConfManager.read_sc_conf(DbCacheConf)
        if cache_conf.use_cache:
            intent_robot_conf = await cls._query_from_cache(robot_id)
        else:
            intent_robot_conf = await cls._query_from_db(db, robot_id)

        if not intent_robot_conf:
            raise BizException("没有找到Intent机器人配置！")
        return intent_robot_conf

    @classmethod
    async def _query_from_cache(cls, robot_id: int) -> Optional[IntentRobotDTO]:
        cache: IntentRobotInfo = manager.get_cache(CacheKey.INTENT_ROBOT)
        return cache.robots.get(robot_id, None)

    @classmethod
    async def _query_from_db(cls, db: AsyncSession, robot_id: int) -> Optional[IntentRobotDTO]:
        stmt = select(IntentRobot).where(
            and_(IntentRobot.intent_robot_id == robot_id, IntentRobot.status == STATUS_VALID)
        )
        record = await db.scalar(stmt)

        if not record:
            return None

        chat_setting = IntentRobotExtraSetting(**record.chat_setting)
        return IntentRobotDTO(
            id=record.intent_robot_id,
            name=record.robot_name,
            desc=record.robot_desc,
            chat_llm_id=record.chat_llm_id,
            vision_llm_id=record.vision_llm_id,
            chat_llm_setting=record.chat_llm_setting,
            vision_llm_setting=record.vision_llm_setting,

            chat_setting=chat_setting,
            vision_setting=record.vision_setting,

            similarity_threshold=record.similarity_threshold,
            vector_similarity_weight=record.vector_similarity_weight,

            top_n=record.top_n,
            top_k=record.top_k,
            tenant_id=record.tenant_id,
            category_ids=record.category_ids,
        )

