import logging
from typing import Optional

from sqlalchemy import and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from qr_orm import const as orm_const
from qr_orm.orm import KnowledgeBase

from rocket_core.server_conf import ConfManager
from quickrag.core.server_conf import DbCacheConf
from quickrag.core import manager, CacheKey
from quickrag.schema import KbDTO, KbInfo, KeywordDTO, RefType
from quickrag.service.common import KeywordService
from quickrag.service.query.service_query import QueryService


class QueryKbListService(QueryService):
    @classmethod
    async def query(
        cls, db: AsyncSession, province_code: str, city_code: str, kb_ids: Optional[list[str]],
        tag_id_list: list[int], tenant_id: str, query: str
    ) -> list[KbDTO]:
        """查询知识库列表入口方法"""
        cache_conf = ConfManager.read_sc_conf(DbCacheConf)
        if cache_conf.use_cache:
            kb_list = await cls._query_from_cache(province_code, city_code, kb_ids, tag_id_list, tenant_id, query)
        else:
            kb_list = await cls._query_from_db(db, province_code, city_code, kb_ids, tag_id_list, tenant_id, query)

        return kb_list

    @classmethod
    async def _query_from_cache(
        cls, province_code: str, city_code: str, kb_ids: Optional[list[str]], tag_id_list: list[int],
        tenant_id: str, query: str
    ) -> list[KbDTO]:
        """从缓存中查询知识库列表，逻辑与数据库查询保持一致"""
        cache: KbInfo = manager.get_cache(CacheKey.KB)
        candidate_kb_list: list[KbDTO] = []
        extra_kb_ids: set[str] = set(kb_ids) if kb_ids else set()

        # 1. 收集全国范围的知识库
        for kb in cache.global_.values():
            if kb.tenant_id == tenant_id:
                candidate_kb_list.append(kb)

        # 2. 收集省分范围的知识库
        if province_code in cache.province:
            for kb in cache.province[province_code].values():
                if kb.tenant_id == tenant_id and kb.scope == orm_const.SCOPE_PROVINCE:
                    candidate_kb_list.append(kb)

        # 3. 收集城市范围的知识库
        if city_code in cache.city:
            for kb in cache.city[city_code].values():
                if kb.tenant_id == tenant_id and kb.scope == orm_const.SCOPE_CITY:
                    candidate_kb_list.append(kb)

        # 4. 收集额外指定ID的知识库（不在上述范围内的）
        existing_ids = {kb.id for kb in candidate_kb_list}
        for kb_id in extra_kb_ids:
            if kb_id not in existing_ids:
                # 从所有可能的来源查找这个kb_id
                kb = cls._find_kb_in_map(cache, kb_id)
                if kb and kb.tenant_id == tenant_id:
                    candidate_kb_list.append(kb)
                    existing_ids.add(kb_id)

        # 处理标签过滤
        return cls._process_tag_filtering(candidate_kb_list, tag_id_list, query, province_code, city_code)

    @classmethod
    async def _query_from_db(
        cls, db: AsyncSession, province_code: str, city_code: str, kb_ids: Optional[list[str]],
        tag_id_list: list[int], tenant_id: str, query: str
    ) -> list[KbDTO]:
        """从数据库中查询知识库列表"""
        # 处理额外的知识库ID
        extra_kb_ids: list[str] = []
        if kb_ids:
            extra_kb_ids.extend(kb_ids)
            logging.debug(f"额外指定的知识库ID数量: {len(extra_kb_ids)}")

        # 构建查询语句
        stmt = select(KnowledgeBase).where(
            and_(
                or_(
                    or_(
                        and_(KnowledgeBase.scope == orm_const.SCOPE_GLOBAL),
                        and_(
                            KnowledgeBase.scope == orm_const.SCOPE_PROVINCE,
                            KnowledgeBase.province_code == province_code
                        ),
                        and_(
                            KnowledgeBase.scope == orm_const.SCOPE_CITY,
                            KnowledgeBase.province_code == province_code,
                            KnowledgeBase.city_code == city_code,
                        ),
                    ),
                    KnowledgeBase.id.in_(extra_kb_ids),
                ),
                KnowledgeBase.status == orm_const.STATUS_VALID,
                KnowledgeBase.tenant_id == tenant_id,
                KnowledgeBase.kb_type.in_([r.value for r in RefType])
            )
        ).execution_options(readonly=True)

        # 执行查询
        candidate_kb_orm_list = list(await db.scalars(stmt))

        if not candidate_kb_orm_list:
            return []

        # 转换为KbDTO列表
        candidate_kb_list = cls._convert_to_dto_list(candidate_kb_orm_list)

        # 处理标签过滤
        return cls._process_tag_filtering(candidate_kb_list, tag_id_list, query, province_code, city_code)

    @classmethod
    def _process_tag_filtering(
        cls, candidate_kb_list: list[KbDTO], tag_id_list: list[int], query: str,
        province_code: str, city_code: str
    ) -> list[KbDTO]:
        """处理标签过滤的公共逻辑"""
        kw_service = KeywordService()
        req_tag_id_set: set[int] = kw_service.filter_valid_tag_id(province_code, city_code, tag_id_list)

        # 一、如果请求中存在有效的tag_id
        if req_tag_id_set:
            kb_list = cls._filter_kb_by_tag(candidate_kb_list, req_tag_id_set)
            return kb_list

        # 二、如果请求中不包含有效的tag_id，尝试用关键词匹配对应的tag
        kw_list: list[KeywordDTO] = kw_service.match_keyword_list(query, province_code, city_code)

        # 2.1 如果没有匹配的关键词，默认命中所有的知识库
        if not kw_list:
            return candidate_kb_list
        else:
            logging.info(f"问答匹配到Tag关键词:({', '.join([kw.keyword for kw in kw_list])})")

        # 2.2 如果匹配到了关键词，搜索范围尝试缩小至关键词关联的tag的知识库
        kw_tag_id_set = set([kw.tag_id for kw in kw_list])
        res_kb_list = cls._filter_kb_by_tag(candidate_kb_list, kw_tag_id_set)
        logging.debug(f"关键词匹配标签后知识库数量: {len(res_kb_list)}")

        return res_kb_list

    @classmethod
    def _filter_kb_by_tag(
        cls, candidate_kb_list: list[KbDTO], candidate_tag_id_set: set[int]
    ) -> list[KbDTO]:
        """根据提供的标签选择知识库"""
        ret_kb_list = []
        for kb in candidate_kb_list:
            sp_tag_id_set = set(kb.special_tag_ids) if kb.special_tag_ids else set()
            if sp_tag_id_set & candidate_tag_id_set:
                ret_kb_list.append(kb)
        return ret_kb_list

    @classmethod
    def _convert_to_dto_list(cls, orm_list: list[KnowledgeBase]) -> list[KbDTO]:
        """将KnowledgeBase对象列表转换为KbDTO列表"""
        dto_list = []
        for kb_orm in orm_list:
            province_code = kb_orm.province_code
            city_code = kb_orm.city_code
            if kb_orm.scope == orm_const.SCOPE_GLOBAL:
                province_code = "000000"
                city_code = "000000"
            elif kb_orm.scope == orm_const.SCOPE_PROVINCE:
                city_code = province_code

            special_tag_ids = [] if not kb_orm.special_tag_ids else kb_orm.special_tag_ids

            kb_dto = KbDTO(
                id=kb_orm.id,
                type=kb_orm.kb_type,  # type: ignore
                domain=kb_orm.kb_domain,
                scope=kb_orm.scope,  # type: ignore
                province_code=province_code,
                city_code=city_code,
                tenant_id=kb_orm.tenant_id,
                name=kb_orm.name,
                desc=kb_orm.description,
                similarity_threshold=kb_orm.similarity_threshold,
                vector_similarity_weight=kb_orm.vector_similarity_weight,
                weight=kb_orm.weight,
                special_tag_ids=special_tag_ids
            )
            dto_list.append(kb_dto)
        return dto_list

    @classmethod
    def _find_kb_in_map(cls, kb_map, kb_id: str) -> Optional[KbDTO]:
        """在kb_map中查找指定ID的知识库"""
        # 全国的不用遍历，因为全包含

        for province_kbs in kb_map.province.values():
            if province_kbs and kb_id in province_kbs:
                return province_kbs[kb_id]

        for city_kbs in kb_map.city.values():
            if city_kbs and kb_id in city_kbs:
                return city_kbs[kb_id]

        return None
