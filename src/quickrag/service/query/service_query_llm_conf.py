from typing import Optional

from sqlalchemy import and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from rocket_util import BizException

from qr_orm import const as orm_const
from qr_orm.orm import LLM

from rocket_core.server_conf import ConfManager
from quickrag.core.server_conf import DbCacheConf
from quickrag.core import manager, <PERSON><PERSON><PERSON>ey
from quickrag.schema import LlmDTO, LlmInfo
from quickrag.service.query.service_query import QueryService


class QueryLLMConfService(QueryService):
    @classmethod
    async def query(cls, db: AsyncSession, llm_id: int) -> LlmDTO:
        cache_conf = ConfManager.read_sc_conf(DbCacheConf)
        if cache_conf.use_cache:
            llm_conf = await cls._query_from_cache(llm_id)
        else:
            llm_conf = await cls._query_from_db(db, llm_id)

        if not llm_conf:
            raise BizException("没有找到大模型配置！")
        return llm_conf

    @classmethod
    async def _query_from_cache(cls, llm_id: int) -> LlmDTO:
        cache: LlmInfo = manager.get_cache(CacheKey.LLM)
        return cache.llms.get(llm_id, None)

    @classmethod
    async def _query_from_db(cls, db: AsyncSession, llm_id: int) -> Optional[LlmDTO]:

        stmt = select(LLM).where(
            and_(
                LLM.llm_id == llm_id,
                LLM.status == orm_const.STATUS_VALID,
            )
        ).execution_options(readonly=True)

        llm: LLM = await db.scalar(stmt)
        if not llm:
            return None

        return LlmDTO(
            id=llm.llm_id,
            llm_type=llm.llm_type, # type: ignore
            alias=llm.alias,
            name=llm.name,
            openai_api_base=llm.openai_api_base,
            openai_api_key=llm.openai_api_key,
            with_thinking=llm.with_thinking,
            extra=llm.extra,
            protocol=llm.protocol # type: ignore
        )