from typing import Optional

from qr_orm.const import STATUS_VALID
from qr_orm.orm_qa_robot import QARobot
from rocket_core import ConfManager
from rocket_util import BizException
from sqlalchemy import and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from quickrag.core import manager, <PERSON><PERSON><PERSON><PERSON>
from quickrag.core.server_conf import DbCacheConf
from quickrag.schema import QARobotInfo, QAChatExtraSetting, QARobotDTO
from quickrag.service.query.service_query import QueryService


class QueryQARobotConfService(QueryService):
    @classmethod
    async def query(cls, db: AsyncSession, robot_id: int) -> QARobotDTO:
        cache_conf = ConfManager.read_sc_conf(DbCacheConf)
        if cache_conf.use_cache:
            qa_robot_conf = await cls._query_from_cache(robot_id)
        else:
            qa_robot_conf = await cls._query_from_db(db, robot_id)

        if not qa_robot_conf:
            raise BizException("没有找到QA机器人配置！")
        return qa_robot_conf

    @classmethod
    async def _query_from_cache(cls, robot_id: int) -> Optional[QARobotDTO]:
        cache: QARobotInfo = manager.get_cache(CacheKey.QA_ROBOT)
        return cache.robots.get(robot_id, None)

    @classmethod
    async def _query_from_db(cls, db: AsyncSession, robot_id: int) -> Optional[QARobotDTO]:
        stmt = select(QARobot).where(and_(QARobot.robot_id == robot_id, QARobot.status == STATUS_VALID))
        record = await db.scalar(stmt)

        if not record:
            return None

        chat_setting = QAChatExtraSetting(**record.chat_setting)
        return QARobotDTO(
            id=record.robot_id,
            name=record.robot_name,
            desc=record.robot_desc,
            chat_llm_id=record.chat_llm_id,
            vision_llm_id=record.vision_llm_id,
            chat_llm_setting=record.chat_llm_setting,
            vision_llm_setting=record.vision_llm_setting,
            chat_setting=chat_setting,
            vision_setting=record.vision_setting,
            enable_llm_solo=record.enable_llm_solo,
            prologue=record.prologue,
            empty_response=record.empty_response,
            similarity_threshold=record.similarity_threshold,
            rerank_threshold=record.rerank_threshold,
            vector_similarity_weight=record.vector_similarity_weight,
            tp_n=record.top_n,
            top_k=record.top_k,
            do_refer=record.do_refer,
            tenant_id=record.tenant_id,
            kb_ids=record.kb_ids,
        )
