from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_

from quickrag.schema import RecommendTool, IntentSceneRelationInfo, ToolDTO, ToolSceneDTO
from qr_orm.orm import Tool, ToolScene, IntentSceneMap
from qr_orm.const import STATUS_VALID
from rocket_core.server_conf import ConfManager
from quickrag.core import manager, CacheKey
from quickrag.core.server_conf import DbCacheConf
from quickrag.service.query.service_query import QueryService


class QueryToolSceneService(QueryService):

    @classmethod
    async def query(cls, db: AsyncSession, intent_id_list: list[int]) -> dict[str, RecommendTool]:
        cache_conf = ConfManager.read_sc_conf(DbCacheConf)
        if cache_conf.use_cache:
            d = await cls._query_from_cache(intent_id_list)
        else:
            d = await cls._query_from_db(db, intent_id_list)

        return d


    @classmethod
    async def _query_from_cache(cls, intent_id_list: list[int]) -> dict[str, RecommendTool]:
        cache: IntentSceneRelationInfo = manager.get_cache(CacheKey.INTENT_SCENE_RELATION)

        ret = {}

        scene_list: list[ToolSceneDTO] = []
        scene2intent = dict()
        for intent_id in intent_id_list:
            if intent_id in cache.intent2scene:
                scene_id = cache.intent2scene[intent_id]
                scene_list.append(cache.scenes[scene_id])
                scene2intent[scene_id] = intent_id

        for scene in scene_list:
            tool: ToolDTO = cache.tools.get(scene.tool_id, None)
            if not tool:
                continue

            ret[str(scene2intent.get(scene.id))] = RecommendTool(
                tool_id=str(scene.tool_id),
                tool_type=tool.type,
                tool_name=tool.name,
                scene_id=str(scene.id),
                scene_code=scene.code,
                scene_name=scene.name,
                scene_type=scene.type,
            )
        return ret


    @classmethod
    async def _query_from_db(cls, db: AsyncSession, intent_id_list: list[int]) -> dict[str, RecommendTool]:
        """根据意图id查询推荐的工具和场景"""
        ret = {}
        stmt = (
            select(
                IntentSceneMap.intent_id,
                Tool.tool_id,
                Tool.tool_type,
                Tool.tool_name,
                ToolScene.scene_type,
                ToolScene.tool_scene_id,
                ToolScene.scene_code,
                ToolScene.scene_name,
            )
            .select_from(IntentSceneMap)
            .join(
                ToolScene,
                and_(
                    ToolScene.tool_scene_id == IntentSceneMap.scene_id,
                    ToolScene.status == STATUS_VALID,
                ),
            )
            .join(
                Tool,
                and_(
                    Tool.tool_id == ToolScene.tool_id,
                    Tool.status == STATUS_VALID,
                ),
            )
            .where(
                and_(
                    IntentSceneMap.intent_id.in_(intent_id_list),
                    IntentSceneMap.status == STATUS_VALID,
                )
            )
            .execution_options(readonly=True)
        )

        result = await db.execute(stmt)

        if not result:
            return ret

        for row in result.fetchall():
            ret[str(row.intent_id)] = RecommendTool(
                tool_id=str(row.tool_id),
                tool_type=row.tool_type,
                tool_name=row.tool_name,
                scene_id=str(row.tool_scene_id),
                scene_code=row.scene_code,
                scene_name=row.scene_name,
                scene_type=row.scene_type,
            )
        return ret
