import asyncio
import json
import logging
import re
from json import JSONDecodeError
from typing import Optional

import json_repair
from pydantic import ValidationError
from sqlalchemy.future import select
from sqlalchemy import and_

from sklearn.metrics.pairwise import cosine_similarity

from qr_orm import const as orm_const
from qr_orm.orm import ConversationIntentInfo
from quickrag.core import manager
from quickrag.service.base import BaseServiceHelper
from quickrag.service.common import EmbeddingService
from quickrag.service.rag.service_rag import BaseRagService
from quickrag.service.retrieval import IntentRetrievalService
from quickrag.schema import (
    IntentRetrievalResult,
    Role,
    Block,
    BlockSource,
    GenerationMode,
    IntentDefItem,
    IntentRecognitionResult,
    Message,
    ReplyMessageFragment,
    IntentRecommend,
    RecommendItem,
    IntentStatus,
)
from quickrag.service.query import QueryToolSceneService
from rocket_util import common, Timer


class IntentRagService(BaseRagService):
    MAX_INTENT_CONTENT_BUFFER_SIZE = 1024

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._ctx_intent_info: Optional[ConversationIntentInfo] = None
        self._raw_reply_piece_list = []

    def _create_retrieval_service(self):
        self._retrieval_service = IntentRetrievalService(
            name=self._name,
            robot_id=self._conversation.intent_robot_id,
            db=self._db,
            province_code=self._province_code,
            city_code=self._city_code,
            role=self._role,
            account=self._account,
            origin_query=self._origin_query,
            group_id=self._group_id,
            data_store=self._get_data_store(),
            queryer=self._get_queryer()
        )

    def _construct_query(self) -> str:
        return self._origin_query

    async def _retrieval(self) -> IntentRetrievalResult:
        await self._retrieval_service.load()
        # 生成问题列表
        complex_query = self._construct_query()
        return await self._retrieval_service.retrieval(complex_query)

    async def _rag(self, queue: asyncio.Queue):
        self._create_retrieval_service()
        # 初始化消息相关的内容
        self._init_message_info()
        # 同时进行知识与意图检索
        self._retrieval_res = await self._retrieval()

        logging.info(f"<【{self._name}】检索记录:{self._retrieval_res.total}条>")
        # 开始流式输出到队列中
        await self._generate(queue)

        self._update_conversation()

    def _update_conversation(self):
        # 更新会话消息
        self._conversation.intent_messages = [m.model_dump() for m in self._message_list]

    async def _generate(self, queue: asyncio.Queue):
        empty_block = self._build_final_block()
        # 加载上下文意图信息
        await self._load_ctx_intent_info()
        logging.info(
            f"<【{self._name}】上一轮意图识别状态:{"null" if self._ctx_intent_info is None else self._ctx_intent_info.recognized_status}>"
        )

        # 不存在上下文，且候选结果为空，返回空块
        if not self._ctx_intent_info and self.total == 0:
            await queue.put(empty_block)
            return

        # 上下文意图不为空，但是状态为0，本次候选结果为空：要判断是否配置开启，允许继续意图识别
        # 当前面轮没识别到意图（存在意图上下文，但是识别结果为不匹配），此时开启这个配置，允许大模型根据聊天记录判断当前是否用户有意图倾向
        if self._ctx_intent_info \
            and self._ctx_intent_info.recognized_status == IntentStatus.NO_MATCH  \
            and self.total == 0 \
            and not self.robot_chat_setting.enable_ongoing_recognition:
            await queue.put(empty_block)
            return

        ctx_intent_def = []

        if self._ctx_intent_info and self._ctx_intent_info.intent_def:
            ctx_intent_def = [IntentDefItem(**item) for item in self._ctx_intent_info.intent_def]

        cur_intent_def = self._build_intent_def()  if self.total > 0 else []

        combined_intent_def = self._combine_indent_def(ctx_intent_def, cur_intent_def)

        # 意图列表为空，不识别直接返回
        if not combined_intent_def:
            await queue.put(empty_block)
            return

        intent_timer = Timer()

        async for block in self._generate_from_kb(combined_intent_def):
            self._reply_piece_list.append(block.message.content)
            await queue.put(block)

        consumed = intent_timer.tik_with_duration()

        # 原始json内容
        raw_reply_content = "".join(self._raw_reply_piece_list)
        # 提取后的content内容
        reply_content = "".join(self._reply_piece_list)

        logging.info(f"<【{self._name}】本轮意图识别:{raw_reply_content}>")

        # 表示什么都没生成，那就直接返回
        if not reply_content and not raw_reply_content:
            await queue.put(empty_block)
            return

        elif raw_reply_content and not reply_content:
            parsed = self._parse_intent_recognition_result(raw_reply_content)
            # 未解析成功，表示json格式不对，直接返回了
            if not parsed:
                await queue.put(empty_block)
                return
        elif not raw_reply_content and reply_content:
            # 不太可能出现这种情况
            await queue.put(empty_block)
            return

        reply_msg = BaseServiceHelper.create_message(
            Role.ASSISTANT,
            raw_reply_content,
            epoch=self.epoch,
            message_id=self._reply_msg_id,
            generation_mode=GenerationMode.INTENT,
            consumed_time=consumed,
        )

        self._reply_token_num = common.num_tokens_from_string(raw_reply_content)

        logging.info(f"<【{self._name}】总计使用token数量:{self._reply_token_num + self._input_token_num}>")
        logging.info(f"<【{self._name}】输入使用token数量:{self._input_token_num}>")
        logging.info(f"<【{self._name}】输出使用token数量:{self._reply_token_num}>")

        self._message_list.append(reply_msg)
        await queue.put(await self._update_ctx_intent_info(raw_reply_content, combined_intent_def))

    def _init_message_info(self):
        # 初始化消息相关的信息
        # 输入消息
        input_message = BaseServiceHelper.create_message(
            self._role, self._origin_query, epoch=self.epoch
        )

        # 历史消息列表
        self._message_list = (
            [BaseServiceHelper.create_message(**msg) for msg in self._conversation.intent_messages]
            if self._conversation.intent_messages
            else []
        )
        self._message_list.append(input_message)
        # 本轮回复的消息ID
        self._reply_msg_id = common.generate_uuid()

    def _build_intent_def(self) -> list[IntentDefItem]:
        secondary_intents = {}

        for chunk in self._retrieval_res.chunks:
            level_1_id = chunk.intent_level_1_id
            level_2_id = chunk.intent_level_2_id

            if level_2_id not in secondary_intents:
                secondary_intents[level_2_id] = IntentDefItem(
                    intent_level_1_id=level_1_id,
                    intent_id=level_2_id,
                    intent_name=chunk.intent_level_2_name or "",
                    intent_desc=chunk.intent_level_2_desc or "",
                    example_questions=[],
                    epoch=self.epoch,
                )

            content = (chunk.content_with_weight or "").strip()
            if content and content not in secondary_intents[level_2_id].example_questions:
                secondary_intents[level_2_id].example_questions.append(content)

        return list(secondary_intents.values())

    def _combine_indent_def(
        self, list1: list[IntentDefItem], list2: list[IntentDefItem]
    ) -> list[IntentDefItem]:
        intent_map: dict[str, IntentDefItem] = {}

        for item in list1 + list2:
            existing = intent_map.get(item.intent_id)

            if existing is None or item.epoch > existing.epoch:
                intent_map[item.intent_id] = item
            elif item.epoch == existing.epoch:
                combined_questions = list(dict.fromkeys(existing.example_questions + item.example_questions))
                intent_map[item.intent_id] = existing.model_copy(update={"example_questions": combined_questions})

        merged_list = list(intent_map.values())
        merged_list.sort(key=lambda x: x.epoch, reverse=True)

        # 剪裁
        trimmed_list = merged_list[: self.robot_chat_setting.intent_def_list_max_size]

        # 按 level_1_id 排序返回
        trimmed_list.sort(key=lambda x: x.intent_level_1_id)

        return trimmed_list

    def _parse_intent_recognition_result(self, text: str) -> Optional[IntentRecognitionResult]:
        """
        从含有前后杂质的文本中提取 JSON，并解析为 IntentRecognitionResult。
        增强容错能力，尝试修复尾部引号缺失或格式轻度错误。
        """
        robot_chat_setting = self.robot_chat_setting
        # 提取JSON
        res = re.search(r"\{[^{}]*}", text, re.DOTALL)

        if not res:
            logging.exception(f"<【{self._name}】意图识别结果为空，原始文本:{text}>")
            return None

        try:
            r = IntentRecognitionResult(**json_repair.loads(res.group()))
            if not robot_chat_setting.enable_intent_confirmation and r.recognized_status ==  IntentStatus.CONFIRMED:
                raise ValueError("当前不支持意图确认！")
            return r
        except ValidationError as e:
            logging.warning(f"<【{self._name}】[数据校验错误] 与模型不匹配: {e}>")
        except JSONDecodeError as e:
            logging.warning(f"<【{self._name}】[JSON解析错误] 与模型不匹配: {e}>")
        except Exception as e:
            logging.warning(f"<【{self._name}】[解析异常]: {e}>")
        return None

    @classmethod
    def _construct_intent_kb(cls, items: list[IntentDefItem]) -> str:
        res = []
        for idx, item in enumerate(items):
            res.append(
                {
                    "intent_number": idx,
                    "intent_name": item.intent_name,
                    "intent_desc": item.intent_desc,
                    "example_questions": item.example_questions
                }
            )
        return json.dumps(res, ensure_ascii=False, indent=2)

    async def _update_ctx_intent_info(self, content: str, intent_def: list[IntentDefItem]) -> Block:
        """再次校验，并保存上下文意图信息"""
        recognized = self._parse_intent_recognition_result(content)

        empty_block = self._build_final_block()
        if not recognized:
            return empty_block

        # 上下文为空，且识别结果为不匹配
        if recognized.recognized_status == IntentStatus.NO_MATCH and not self._ctx_intent_info:
            return empty_block

        related_intent_numbers = recognized.related_intent_numbers

        recognized_intent_id = 0
        recognized_intent_name = ""

        recommend_list = []
        # 模糊匹配多意图 or 识别到精确意图 or 确认意图
        if recognized.recognized_status in (IntentStatus.LOCKED, IntentStatus.CONFIRMED):
            if (not related_intent_numbers) or len(related_intent_numbers) != 1:
                return empty_block

            n = related_intent_numbers[0]
            if n < 0 or n >= len(intent_def):
                return empty_block
            else:
                recognized_intent_id = intent_def[n].intent_id
                recognized_intent_name = intent_def[n].intent_name
            candidate_list = [intent_def[n]]
            recommend_list.extend(await self._pick_recommend_list(candidate_list))

        elif recognized.recognized_status == IntentStatus.FUZZY:
            if (not related_intent_numbers) or len(related_intent_numbers) > len(intent_def):
                return empty_block

            candidate_list= []
            for n in related_intent_numbers:
                if n < 0 or n >= len(intent_def):
                    continue
                candidate_list.append(intent_def[n])
            if len(candidate_list) > 0:
                recommend_list.extend(await self._pick_recommend_list(candidate_list))
        else:
            pass

        # 若当前上下文为空，则新建
        if not self._ctx_intent_info:
            # 创建
            self._ctx_intent_info = ConversationIntentInfo()
            self._ctx_intent_info.intent_info_id = manager.next_id()
            self._ctx_intent_info.cid = self._conversation.cid
            self._ctx_intent_info.status = orm_const.STATUS_VALID
            self._db.add(self._ctx_intent_info)
            self._conversation.ctx_intent_info_id = self._ctx_intent_info.intent_info_id

        # 更新信息
        self._ctx_intent_info.recognized_status = recognized.recognized_status
        self._ctx_intent_info.locked_intent_id = recognized_intent_id
        self._ctx_intent_info.intent_def = [item.model_dump() for item in intent_def] if intent_def else []

        return self._build_final_block(
            recognized_status=IntentStatus(recognized.recognized_status),
            content="".join(self._reply_piece_list),
            recognized_intent_id=str(recognized_intent_id),
            recognized_intent_name=recognized_intent_name,
            recommend_list=recommend_list,
        )

    async def _generate_from_kb(self, intent_def: list):
        robot_chat_setting = self.robot_chat_setting
        if robot_chat_setting.stream:
            async for block in self._stream_block(GenerationMode.INTENT, intent_def=intent_def):
                yield block
        else:
            async for block in self._non_stream_block(GenerationMode.INTENT, intent_def=intent_def):
                yield block

    def _get_block_source(self) -> BlockSource:
        return BlockSource.INTENT

    def _construct_chat_msg_list(self, generation_mode: GenerationMode, **kwargs) -> list[Message]:
        robot_chat_setting = self.robot_chat_setting

        intent_def = kwargs.get("intent_def", [])
        scope_desc = "{" + ",".join([str(i) for i in range(len(intent_def))]) + "}"

        if robot_chat_setting.enable_intent_confirmation:
            prompt = self.robot_chat_setting.recognization_system_prompt
            # 确认意图时，需要传递上轮的意图状态
            previous_status = self._ctx_intent_info.recognized_status if self._ctx_intent_info else 0
            prompt = prompt.replace("{previous_recognized_status}", str(previous_status))
        else:
            prompt = self.robot_chat_setting.recognization_no_confirm_system_prompt

        prompt = prompt.replace("{intent_def}", f"```json\n{self._construct_intent_kb(intent_def)}\n```")
        prompt = prompt.replace("{intent_number_scope}", scope_desc)

        msg_list = [BaseServiceHelper.create_message(role=Role.SYSTEM, content=prompt, epoch=self.epoch)]  # type: ignore

        # 根据配置决定哪些消息送入大模型
        if self.robot_chat_setting.only_user_message:
            part = [msg for msg in self._message_list if BaseServiceHelper.is_a_questioner_role(msg.role)]
        else:
            part = [msg for msg in self._message_list if msg.role != Role.SYSTEM]

        part = part[-self.robot_chat_setting.max_memory_chat_msg_size:]
        msg_list.extend(part)
        return msg_list

    def _on_last_block(self, block: Block, generation_mode: GenerationMode):
        pass

    def _before_parsing(self, target_field: str, buffer: str, fragments: list[str]) -> tuple[bool, str]:
        max_buffer_size = self.MAX_INTENT_CONTENT_BUFFER_SIZE
        # 从buffer中定位目标字段位置
        target_key_index = buffer.find(f'"{target_field}"')
        # 没有找到
        if target_key_index == -1:
            # 截取，防止buffer过大（实际不太可能）
            buffer = buffer[-max_buffer_size:] if len(buffer) > max_buffer_size else buffer
            return False, buffer

        # 找到target，尝试找后面`冒号`与`引号`的位置
        colon_index = buffer.find(':', target_key_index)
        quote_start_index = buffer.find('"', colon_index)

        # 无法定位`冒号`或`引号`，说明后面的块内容还待生成，跳出到下一轮重新找
        if colon_index == -1 or quote_start_index == -1:
            return False, buffer

        # 若：
        #   `引号`是当前buffer最后字符，将buffer清空
        # 否则：
        #   解析位置变成`引号`的index+1（为了解决buffer还有未处理完的部分）
        #   需要调用`self._in_parsing(buffer, fragments)`处理额外的部分
        if (quote_start_index + 1) < len(buffer):
            buffer = buffer[quote_start_index + 1:]
            self._in_parsing(buffer, fragments)

        return True, ""

    def _in_parsing(self, buffer: str, fragments: list[str]) -> tuple[bool, str]:
        parsed = False
        piece = buffer

        end_quote_index = self._find_unescaped_quote(buffer)
        if end_quote_index is not None:
            piece = buffer[:end_quote_index]
            parsed = True
        # 处理\ + n的情况，替换成\n\n
        if piece == "n" and len(fragments) > 0 and fragments[-1] == "\\":
            fragments[-1] = "\n\n"
        else:
            fragments.append(piece)

        return parsed, ""

    async def _stream_block(self, generation_mode: GenerationMode, **kwargs):
        """重写父类方法，需要从返回的流式json中取到content字段的内容，然后流式返回"""
        timer = Timer()
        msg_list = self._construct_chat_msg_list(generation_mode, **kwargs)
        msg_list, iter_func = BaseServiceHelper.adapt_thinking_mode(
            msg_list, self.robot_chat_setting, self.chat_model_conf
        )
        self._input_token_num = BaseServiceHelper.count_token_num(msg_list)

        if kwargs and 'target_filed' in kwargs:
            target_field = kwargs['target_filed']
        else:
            target_field = "content"

        buffer = ""
        in_parsing = False
        parsed = False
        fragments = []

        async for is_last_chunk, chunk in iter_func(BaseServiceHelper.stream(msg_list, self.llm_handler)):
            self._raw_reply_piece_list.append(chunk)
            # 如果已经解析完毕，continue
            if parsed:
                continue

            buffer += chunk

            if not in_parsing:
                in_parsing, buffer = self._before_parsing(target_field, buffer, fragments)
            else:
                parsed, buffer = self._in_parsing(buffer, fragments)

            if not fragments:
                continue

            # 处理\ + n的情况
            if len(fragments) > 0 and fragments[-1] == "\\":
                continue
            yield self._build_block("".join(fragments))
            fragments.clear()

        logging.info(timer.tik_with_desc(f"【{self._name}】大模型回复"))

    @classmethod
    def _find_unescaped_quote(cls, text: str) -> Optional[int]:
        for current_index in range(len(text)):
            if text[current_index] == '"':
                backslash_count = 0
                back_index = current_index - 1
                while back_index >= 0 and text[back_index] == '\\':
                    backslash_count += 1
                    back_index -= 1
                if backslash_count % 2 == 0:
                    return current_index
        return None

    def _build_block(self, raw_content: str) -> Block:
        try:
            decoded_content = json.loads(f'"{raw_content}"')
        except json.JSONDecodeError:
            decoded_content = raw_content

        decoded_content = decoded_content.replace("}", "").replace("```", "")

        block = Block()
        fragment = ReplyMessageFragment()
        fragment.message_id = self._reply_msg_id
        fragment.role = Role.ASSISTANT
        fragment.created_at = common.get_time()
        fragment.content = decoded_content
        block.message = fragment
        block.conversation_id = str(self._conversation.cid)
        block.group_id = self._conversation.biz_id  # type: ignore
        block.generation_mode = None
        block.source = self._get_block_source()
        return block

    def _build_final_block(
        self,
        recognized_status: Optional[IntentStatus] = None,
        content: Optional[str] = None,
        recognized_intent_id: Optional[str] = None,
        recognized_intent_name: Optional[str] = None,
        recommend_list: Optional[list[RecommendItem]] = None,
    ):
        block = Block()
        block.conversation_id = str(self._conversation.cid)
        block.group_id = self._group_id
        block.source = self._get_block_source()
        block.finished = True
        block.generation_mode = GenerationMode.INTENT

        fragment = ReplyMessageFragment()
        fragment.message_id = self._reply_msg_id
        fragment.role = Role.ASSISTANT
        fragment.created_at = common.get_time()
        fragment.content = ""
        block.message = fragment

        extra = IntentRecommend()
        block.extra = extra

        if recognized_status is None:
            extra.recognized_status = IntentStatus.NO_MATCH
            return block

        extra.recognized_status = recognized_status
        extra.recognized_intent_id = recognized_intent_id
        extra.recognized_intent_name = recognized_intent_name
        extra.content = content
        extra.recommend_list = recommend_list

        return block

    async def _load_ctx_intent_info(self):
        if self._ctx_intent_info or self._conversation.ctx_intent_info_id <= 0:
            return

        stmt = select(ConversationIntentInfo).where(
            and_(
                ConversationIntentInfo.cid == self._conversation.cid,
                ConversationIntentInfo.intent_info_id == self._conversation.ctx_intent_info_id,
                ConversationIntentInfo.status == orm_const.STATUS_VALID,
            )
        )
        self._ctx_intent_info = await self._db.scalar(stmt)

    async def _pick_recommend_list(self, candidate_list: list[IntentDefItem]) -> list[RecommendItem]:
        # 不使用向量排序：直接取第一个 example
        if not self.robot_chat_setting.sort_examples_by_vector:
            return [
                RecommendItem(
                    intent_id=c.intent_id,
                    intent_name=c.intent_name,
                    content=c.example_questions[0]
                )
                for c in candidate_list
                if c.example_questions
            ]

        # 使用向量排序
        es = EmbeddingService()

        # 构造 (question, intent_id, intent_name) 三元组
        examples: list[tuple[str, str, str]] = [
            (q, c.intent_id, c.intent_name)
            for c in candidate_list
            for q in c.example_questions
        ]

        if not examples:
            return []

        # 向量编码 + 相似度计算
        q_vec, doc_vec_list = await es.encode_query_and_docs(self._origin_query, [q for q, _, _ in examples])
        sim_scores = cosine_similarity([q_vec], doc_vec_list)[0]

        # intent_id -> (score, question, intent_name)
        intent_id_to_best: dict[str, tuple[float, str, str]] = {}

        for score, (question, intent_id, intent_name) in zip(sim_scores, examples):

            if intent_id not in intent_id_to_best or score > intent_id_to_best[intent_id][0]:
                intent_id_to_best[intent_id] = (score, question, intent_name)

        # 根据配置只取评分最高的前N个
        intent_id_to_best = self.keep_top_n(intent_id_to_best)

        # 构造推荐项（包括工具）
        tool_service = QueryToolSceneService()
        intent_id_to_tool = await tool_service.query(self._db, [int(intent_id) for intent_id in intent_id_to_best])
        recommend_list = []
        for intent_id, (_, question, intent_name) in intent_id_to_best.items():
            tool = intent_id_to_tool.get(intent_id, None)
            item = RecommendItem(
                intent_id=intent_id,
                intent_name=intent_name,
                content=question,
                tool=tool
            )
            recommend_list.append(item)
        return recommend_list

    def keep_top_n(self, intent_dict: dict) -> dict:
        """
        保留字典中分数最高的前n个条目
        """
        robot_chat_setting = self.robot_chat_setting
        # 按score降序排序（取前n个）
        sorted_items = sorted(
            intent_dict.items(),
            key=lambda x: x[1][0],
            reverse=True
        )[:robot_chat_setting.recommend_size]

        # 2. 转换回字典
        return dict(sorted_items)

    async def _non_stream_block(self, generation_mode: GenerationMode, **kwargs):
        llm_handler = self.llm_handler
        timer = Timer()
        msg_list = self._construct_chat_msg_list(generation_mode, **kwargs)
        msg_list, iter_func = BaseServiceHelper.adapt_thinking_mode(
            msg_list, self.robot_chat_setting, self.chat_model_conf
        )
        self._input_token_num = BaseServiceHelper.count_token_num(msg_list)

        langchain_messages = BaseServiceHelper.convert_to_langchain_messages(msg_list)
        r = await llm_handler.chat_model.agenerate([langchain_messages])
        try:
            text = r.generations[0][0].text
            recognized = self._parse_intent_recognition_result(text)
            if recognized is None:
                raise Exception("")

            self._raw_reply_piece_list.append(recognized.model_dump_json())
            yield self._build_block(recognized.content)
        except Exception as e:
            logging.exception(f"意图识别失败，{str(e)}", exc_info=True)
            yield self._build_block("")

        logging.info(timer.tik_with_desc(f"【{self._name}】大模型回复"))
