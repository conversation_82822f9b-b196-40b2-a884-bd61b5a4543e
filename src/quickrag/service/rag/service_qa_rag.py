import asyncio
import json
import logging
import re
from collections import defaultdict
from typing import AsyncGenerator

from rocket_core import ConfManager
from rocket_util import common, BizException, Timer

from qr_orm import const as orm_const

from quickrag.core.server_conf import AIAuditConf
from quickrag.service.base import BaseServiceHelper
from quickrag.service.rag.service_rag import BaseRagService
from quickrag.service.common import AIAuditService
from quickrag.service.retrieval import KnowledgeRetrievalService
from quickrag.schema import (
    ChunkReference,
    DocumentReference,
    QARetrievalResult,
    Role,
    Block,
    ReplyMessageFragment,
    BlockSource,
    GenerationMode,
    QAChunk,
    RefType,
    KnowledgeExtra,
    GroupExtra,
    BlockReference,
    Message
)


class QARagService(BaseRagService):
    QUOTE_PATTERN = re.compile(r"##\s*\d+\s*\$\$?")

    KB_TYPE_MAP = {
        orm_const.KB_TYPE_FAQ: "问答类知识",
        orm_const.KB_TYPE_DOCUMENT: "文档类知识",
        orm_const.KB_TYPE_HISTORY: "会话类知识",
    }

    # 知识类型与质量映射
    KB_QUALITY_MAP = {
        orm_const.KB_TYPE_FAQ: "高",
        orm_const.KB_TYPE_DOCUMENT: "中",
        orm_const.KB_TYPE_HISTORY: "低",
    }

    def __init__(self, ctx, email: str, tag_id_list: list[int], **kwargs):
        super().__init__(**kwargs)
        self._ctx = ctx
        self._email = email
        self._tag_id_list = tag_id_list

        self._chunk_reference_list: list[ChunkReference] = []
        self._document_reference_list: list[DocumentReference] = []

    async def _rag(self, queue: asyncio.Queue):
        if not await self._audit_question(queue):
            return

        self._create_retrieval_service()
        # 初始化消息相关的内容
        self._init_message_info()
        # 同时进行知识与意图检索
        self._retrieval_res = await self._retrieval()

        # 开始流式输出到队列中
        await self._generate(queue)

        if not await self._audit_answer(queue):
            return

        self._update_conversation()

    def _create_retrieval_service(self):
        self._retrieval_service = KnowledgeRetrievalService(
            name=self._name,
            tag_id_list=self._tag_id_list,
            robot_id=self._conversation.qa_robot_id,
            db=self._db,
            province_code=self._province_code,
            city_code=self._city_code,
            role=self._role,
            account=self._account,
            origin_query=self._origin_query,
            group_id=self._group_id,
            data_store=self._get_data_store(),
            queryer=self._get_queryer()
        )

    def _construct_query(self) -> str:
        if self.robot_chat_setting.enable_question_gen:
            raise BizException("暂未实现自动问题分析功能")
        else:
            msg_list = [
                m for m in self._message_list if BaseServiceHelper.is_a_questioner_role(m.role)
            ][-self.robot_chat_setting.latest_question_n :]
            if not msg_list:
                raise BizException("提问人的消息为空")
            return " ".join([msg.content for msg in msg_list])

    async def _retrieval(self) -> QARetrievalResult:
        await self._retrieval_service.load()
        # 生成问题列表
        complex_query = self._construct_query()
        logging.info(f"<【{self._name}】ComplexQuery:{complex_query}>")
        res = await self._retrieval_service.retrieval(complex_query)

        # 兜底策略：当使用上下文检索为空时，退化成使用query检索
        if res.total == 0 and len(complex_query) > len(self._origin_query):
            res = await self._retrieval_service.retrieval(self._origin_query)

        logging.info(f"<【{self._name}】检索结果数量:{res.total}>")
        return res

    async def _generate(self, queue: asyncio.Queue):
        block = None
        gen_timer = Timer()

        # 无相关的数据
        if self.total <= 0:
            async for block in self._generate_by_llm():  # type: ignore
                if block and block.message and block.message.content:
                    # 保存回复的消息片段
                    self._reply_piece_list.append(block.message.content)
                await queue.put(block)
        else:
            async for block in self._generate_from_kb():  # type: ignore
                if block and block.message and block.message.content:
                    # 保存回复的消息片段
                    self._reply_piece_list.append(block.message.content)
                await queue.put(block)

        consumed = gen_timer.tik_with_duration()

        reply_msg = BaseServiceHelper.create_message(
            Role.ASSISTANT,
            "".join(self._reply_piece_list),
            epoch=self.epoch,
            message_id=self._reply_msg_id,
            generation_mode=block.generation_mode,
            consumed_time=consumed,
        )

        self._reply_token_num = common.num_tokens_from_string(reply_msg.content)

        logging.info(f"<【{self._name}】总计使用token数量:{self._reply_token_num + self._input_token_num}>")
        logging.info(f"<【{self._name}】输入使用token数量:{self._input_token_num}>")
        logging.info(f"<【{self._name}】输出使用token数量:{self._reply_token_num}>")

        self._message_list.append(reply_msg)

    def _update_conversation(self):
        # 更新会话消息
        self._conversation.qa_messages = [m.model_dump() for m in self._message_list]

        if not self._conversation.qa_reference:
            self._conversation.qa_reference = {}
        if self._chunk_reference_list:
            qa_reference = {k: v for k, v in self._conversation.qa_reference.items()}

            qa_reference[self._reply_msg_id] = [  # type: ignore
                c.model_dump() for c in self._chunk_reference_list
            ]
            self._conversation.qa_reference = qa_reference

    async def _generate_by_llm(self) -> AsyncGenerator[Block, None]:
        if self.robot.enable_llm_solo:
            async for block in self._stream_block(GenerationMode.BY_LLM):
                yield block
        else:
            b = Block()
            fragment = ReplyMessageFragment()
            b.group_id = self._group_id
            b.conversation_id = str(self._conversation.cid)
            b.generation_mode = GenerationMode.DEFAULT
            b.source = self._get_block_source()
            b.finished = True

            if self.robot.empty_response:
                fragment.message_id = self._reply_msg_id
                fragment.role = Role.ASSISTANT
                fragment.created_at = common.get_time()
                fragment.content = self.robot.empty_response  # type: ignore
            b.message = fragment
            yield b

    @classmethod
    def _construct_kb_prompt(cls, chunks: list[QAChunk]) -> str:
        pos = 0

        kb_template = {
            "问答类知识": [],
            "文档类知识": defaultdict(lambda: {"文档名称": "", "相关知识": []}),
            "会话类知识": [],
        }

        # 初始化结构：类型名称 -> 数据列表
        for chunk in chunks:
            kb_type = chunk.kb_type
            kb_type_name = cls.KB_TYPE_MAP.get(chunk.kb_type)
            if not kb_type_name:
                continue

            if kb_type == orm_const.KB_TYPE_DOCUMENT:
                doc_id = chunk.doc_id
                doc_info = kb_template[kb_type_name][doc_id]
                if not doc_info["文档名称"]:
                    doc_info["文档名称"] = chunk.doc_kwd
                doc_info["相关知识"].append(
                    {"知识编号": pos, "知识内容": chunk.content_with_weight.rstrip()}
                )
            else:
                kb_template[kb_type_name].append(
                    {"知识编号": pos, "知识内容": chunk.content_with_weight.rstrip()}
                )

            pos += 1

        # 构造最终 JSON 列表
        kb = []
        for kb_type in cls.KB_TYPE_MAP.keys():
            kb_type_name = cls.KB_TYPE_MAP.get(kb_type)
            data = kb_template[kb_type_name]
            # 如果该类型没有数据就跳过
            if not data:
                continue

            if kb_type == orm_const.KB_TYPE_DOCUMENT:
                data = list(data.values())

            kb.append(
                {
                    "类型": kb_type_name,
                    "质量": cls.KB_QUALITY_MAP.get(kb_type, "未知"),
                    "数据": data,
                }
            )

        return f"```json\n{json.dumps(kb, indent=2, ensure_ascii=False)}\n```"

    def _init_reference_info(self) -> None:
        res = self._retrieval_res
        if not res or res.total == 0 or not res.chunks:
            return

        chunk_ref_list = []

        for c in res.chunks:
            chunk_ref = ChunkReference()
            chunk_ref.chunk_id = c.chunk_id
            chunk_ref.document_id = c.doc_id
            chunk_ref.document_name = c.doc_kwd
            chunk_ref.kb_id = c.kb_id
            chunk_ref.content = c.content_with_weight

            if c.kb_type == RefType.FAQ.value:
                chunk_ref.ref_type = RefType.FAQ
            elif c.kb_type == RefType.DOCUMENT.value:
                chunk_ref.ref_type = RefType.DOCUMENT
            elif c.kb_type == RefType.HISTORY.value:
                chunk_ref.ref_type = RefType.HISTORY
            else:
                chunk_ref.ref_type = RefType.HISTORY

            chunk_ref.image_id = c.image_id
            chunk_ref.positions = c.positions
            extra = {k: str(v) for k, v in c.extra.items() if v is not None}
            if chunk_ref.ref_type in (RefType.FAQ, RefType.DOCUMENT):
                chunk_ref.extra = KnowledgeExtra(**extra)
            else:
                chunk_ref.extra = GroupExtra(**extra)
            chunk_ref_list.append(chunk_ref)

        self._chunk_reference_list.extend(chunk_ref_list)

    def _update_reference_info(self, last_content: str) -> GenerationMode:
        # 未开启引用时
        if not self.robot_chat_setting.enable_citation:
            document_dict = dict()
            for i, chunk_ref in enumerate(self._chunk_reference_list):
                if chunk_ref.document_id not in document_dict:
                    dr = DocumentReference()
                    dr.document_id = chunk_ref.document_id
                    dr.document_name = chunk_ref.document_name
                    dr.ref_type = chunk_ref.ref_type
                    dr.extra = chunk_ref.extra
                    dr.count = 1
                    document_dict[chunk_ref.document_id] = dr
                else:
                    dr = document_dict[chunk_ref.document_id]
                    dr.count += 1
            self._document_reference_list.extend([v for k, v in document_dict.items()])
            return GenerationMode.REF_KB

        # 需要人工拼凑完整的回复内容
        reply_content = f"{"".join(self._reply_piece_list)}{last_content}"
        matches = self.QUOTE_PATTERN.findall(reply_content)
        if not matches:
            matches = []

        valid_quote_set = set()
        for m in matches:
            m = m.replace("#", "").replace("$", "").replace(" ", "")
            try:
                quote_idx = int(m)
                if 0 <= quote_idx < len(self._chunk_reference_list):
                    valid_quote_set.add(quote_idx)
            except ValueError:
                continue

        document_dict = dict()
        for i, chunk_ref in enumerate(self._chunk_reference_list):
            if i not in valid_quote_set:
                chunk_ref.quoted = False

            if chunk_ref.quoted:
                if chunk_ref.document_id not in document_dict:
                    dr = DocumentReference()
                    dr.document_id = chunk_ref.document_id
                    dr.document_name = chunk_ref.document_name
                    dr.ref_type = chunk_ref.ref_type
                    dr.extra = chunk_ref.extra
                    dr.count = 1
                    document_dict[chunk_ref.document_id] = dr
                else:
                    dr = document_dict[chunk_ref.document_id]
                    dr.count += 1

        self._document_reference_list.extend([v for k, v in document_dict.items()])

        if not valid_quote_set:
            return GenerationMode.BY_LLM
        else:
            return GenerationMode.REF_KB

    async def _generate_from_kb(self):
        if self.robot_chat_setting.enable_kg:
            raise BizException("暂未实现知识图谱功能")

        if self.robot_chat_setting.enable_tag:
            raise BizException("暂未实现知识标签功能")

        async for block in self._stream_block(GenerationMode.REF_KB):
            yield block

    def _prune_qa_res(self):
        """
        按照知识类、文档类、会话类的顺序进行排序，其中文档类中按照doc_id进行排序
        约定：知识库部分提示词不能超过max_tokens的85%
        """
        res = self._retrieval_res
        robot_chat_setting = self.robot_chat_setting
        # 执行一次线性稳定排序，知识>>文档>会话
        faq_chunks = []
        doc_chunks = []
        group_chunks = []

        for i, c in enumerate(res.chunks):
            if c.kb_type == RefType.FAQ.value:
                faq_chunks.append(c)
            elif c.kb_type == RefType.DOCUMENT.value:
                doc_chunks.append(c)
            else:
                group_chunks.append(c)

        # 对于文档类，按照doc_id进行排序
        doc_chunks.sort(key=lambda x: x.doc_id, reverse=True)
        # 排序结果
        chunks = [*faq_chunks, *doc_chunks, *group_chunks]

        # 根据使用的token，对chunks进行截断
        used_token_count = 0
        chunks_num = 0
        for i, c in enumerate(chunks):
            used_token_count += common.num_tokens_from_string(c.content_with_weight)
            chunks_num += 1
            if robot_chat_setting.max_tokens * 0.85 < used_token_count:
                logging.warning(
                    f"【{self._name}】提示词超出限制，已截断: {chunks_num}/{len(res.chunks)}"
                )
                break
        chunks = chunks[:chunks_num]
        self._retrieval_res = QARetrievalResult(total=len(chunks), chunks=chunks)

    def _init_message_info(self):
        # 初始化消息相关的信息
        # 输入消息
        input_message = BaseServiceHelper.create_message(
            self._role, self._origin_query, epoch=self.epoch
        )

        # 历史消息列表
        self._message_list = (
            [BaseServiceHelper.create_message(**msg) for msg in self._conversation.qa_messages]
            if self._conversation.qa_messages
            else []
        )
        self._message_list.append(input_message)
        # 本轮回复的消息ID
        self._reply_msg_id = common.generate_uuid()

    def _construct_chat_msg_list(self, generation_mode: GenerationMode, **kwargs) -> list[Message]:
        if generation_mode == GenerationMode.BY_LLM:
            return self._construct_solo_msg_list()
        else:
            return self._construct_rag_msg_list()

    def _construct_solo_msg_list(self) -> list[Message]:
        msg_list = [
            BaseServiceHelper.create_message(
                Role.SYSTEM, self.robot_chat_setting.solo_prompt_system, epoch=self.epoch
            )
        ]
        if self.robot_chat_setting.only_user_message:
            msg_list.extend(
                [
                    msg for msg in self._message_list if BaseServiceHelper.is_a_questioner_role(msg.role)
                ][-self.robot_chat_setting.latest_message_n :]
            )
        else:
            msg_list.extend(
                [
                    msg for msg in self._message_list if msg.role != Role.SYSTEM
                ][-self.robot_chat_setting.latest_message_n :]
            )

        _, msg_list = BaseServiceHelper.prune_message_list(
            msg_list, self.robot_chat_setting.max_tokens
        )

        return msg_list

    def _construct_rag_msg_list(self) -> list[Message]:
        # 根据最大token数进行截断
        self._prune_qa_res()
        self._init_reference_info()

        res = self._retrieval_res
        # 构造知识库部分的prompt
        kb_part_prompt = self._construct_kb_prompt(res.chunks)

        quote_scope_desc = "{" + ",".join([str(i) for i in range(len(self._chunk_reference_list))]) + "}"
        system_content = self.robot_chat_setting.rag_prompt_system.replace(
            "{quote_scope}", f"{quote_scope_desc}"
        )
        system_content = system_content.replace("{kb}", kb_part_prompt)

        msg_list = [BaseServiceHelper.create_message(role=Role.SYSTEM, content=system_content, epoch=self.epoch)]
        if self.robot_chat_setting.only_user_message:
            msg_list.extend(
                [
                    msg for msg in self._message_list if BaseServiceHelper.is_a_questioner_role(msg.role)
                ][-self.robot_chat_setting.latest_message_n :]
            )
        else:
            msg_list.extend(
                [
                    msg for msg in self._message_list if msg.role != Role.SYSTEM
                ][-self.robot_chat_setting.latest_message_n :]
            )
        _, msg_list = BaseServiceHelper.prune_message_list(
            msg_list, self.robot_chat_setting.max_tokens
        )
        return msg_list

    def _get_block_source(self) -> BlockSource:
        return BlockSource.QA

    def _on_last_block(self, block: Block, generation_mode: GenerationMode):
        if generation_mode.REF_KB:
            block.generation_mode = self._update_reference_info(block.message.content)
            block.extra = BlockReference(
                chunk_reference_list=self._chunk_reference_list,
                document_reference_list=self._document_reference_list,
            )

    def _on_risk_triggered(self):
        self._ctx.on_risk_triggered()

    async def _audit_question(self, queue) -> bool:
        conf = ConfManager.read_sc_conf(AIAuditConf)
        if not conf.open_audit:
            return True

        timer = Timer()
        query = self._origin_query
        email = self._email
        audit_service = AIAuditService()
        passed = await audit_service.audit(query, email)

        logging.info(timer.tik_with_desc(f"【{self._name}】Question_AI安全审计"))

        if not passed:
            await queue.put(self._build_risk_block(conf.hint, True))
            self._on_risk_triggered()
            return False
        else:
            return True

    async def _audit_answer(self, queue) -> bool:
        conf = ConfManager.read_sc_conf(AIAuditConf)
        if not conf.open_audit:
            return True

        content = ",".join(self._reply_piece_list)
        if not content:
            return True

        timer = Timer()
        email = self._email
        audit_service = AIAuditService()
        passed = await audit_service.audit(content, email)
        logging.info(timer.tik_with_desc(f"【{self._name}】Answer_AI安全审计"))
        if not passed:
            await queue.put(self._build_risk_block(conf.hint, False))
            self._on_risk_triggered()
            return False
        else:
            return True

    def _build_risk_block(self, hint: str, from_question: bool) -> Block:
        fragment = ReplyMessageFragment()
        fragment.created_at = common.get_time()
        fragment.message_id = self._reply_msg_id
        fragment.role = Role.ASSISTANT
        fragment.content = hint

        block = Block()
        block.message =  fragment
        block.conversation_id = str(self._conversation.cid)
        block.group_id = self._conversation.biz_id
        block.source = BlockSource.RISK
        block.finished = True
        block.generation_mode = GenerationMode.RISK_QUESTION if from_question else GenerationMode.RISK_ANSWER
        return block
