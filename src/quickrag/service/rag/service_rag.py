import logging
from asyncio import Queue
from typing import Optional
from abc import abstractmethod, ABC

from sqlalchemy.ext.asyncio import AsyncSession

from rocket_util import common, Timer
from rocket_nlp.query import FulltextQueryer

from quickrag.schema import (
    Block,
    Role,
    GenerationMode,
    Message,
    BlockSource,
    ReplyMessageFragment,
)
from qr_orm.orm import Conversation
from quickrag.service.base import BaseService, BaseServiceHelper
from quickrag.service.retrieval import BaseRetrievalService
from quickrag.core import queryer, data_store, ESStore


class RagInterface(ABC):
    @abstractmethod
    async def rag(self, queue: Queue):
        pass


class BaseRagService(BaseService, RagInterface, ABC):
    def __init__(
        self,
        name: str,
        conversation: Conversation,
        db: Optional[AsyncSession],
        group_id: str,
        account: str,
        origin_query: str,
        province_code: str,
        city_code: str,
        role: Role,
    ):
        # 传入的参数
        self._name = name
        self._conversation = conversation
        self._db = db
        self._group_id = group_id
        self._account = account
        self._origin_query = origin_query
        self._province_code = province_code
        self._city_code = city_code
        self._role = role

        # 定义的参数
        self._reply_msg_id: Optional[str] = None
        self._reply_piece_list: list[str] = []
        self._message_list: Optional[list[Message]] = None
        self._input_token_num: int = 0
        self._reply_token_num: int = 0

        self._retrieval_service: Optional[BaseRetrievalService] = None
        self._retrieval_res = None

    async def rag(self, queue: Queue):
        rag_timer = Timer()
        await self._rag(queue)
        logging.info(rag_timer.tik_with_desc(f"【{self._name}】RAG"))

    @property
    def total(self):
        if self._retrieval_res is None:
            return 0
        return self._retrieval_res.total

    @property
    def epoch(self):
        return self._conversation.epoch

    @property
    def robot_chat_setting(self):
        return self._retrieval_service.robot_chat_setting

    @property
    def llm_handler(self):
        return self._retrieval_service.llm_handler

    @property
    def robot(self):
        return self._retrieval_service.robot

    @property
    def chat_model_conf(self):
        return self._retrieval_service.llm_handler.chat_model_conf

    @abstractmethod
    async def _rag(self, queue: Queue):
        pass

    @abstractmethod
    async def _generate(self, queue: Queue):
        pass

    @abstractmethod
    def _create_retrieval_service(self):
        pass

    @abstractmethod
    def _retrieval(self):
        pass

    @abstractmethod
    def _construct_chat_msg_list(self, generation_mode: GenerationMode, **kwargs) -> list[Message]:
        pass

    @abstractmethod
    def _construct_query(self) -> str:
        pass

    @abstractmethod
    def _get_block_source(self) -> BlockSource:
        pass

    @abstractmethod
    def _on_last_block(self, block: Block, generation_mode: GenerationMode) -> Block:
        pass

    @abstractmethod
    def _update_conversation(self):
        pass

    @abstractmethod
    def _init_message_info(self):
        pass

    @classmethod
    def _get_data_store(cls) -> ESStore:
        return data_store

    @classmethod
    def _get_queryer(cls) -> FulltextQueryer:
        return queryer

    async def _stream_block(self, generation_mode: GenerationMode, **kwargs):
        timer = Timer()
        msg_list = self._construct_chat_msg_list(generation_mode, **kwargs)

        msg_list, iter_func = BaseServiceHelper.adapt_thinking_mode(
            msg_list, self.robot_chat_setting, self.chat_model_conf
        )

        self._input_token_num = BaseServiceHelper.count_token_num(msg_list)

        async for last_bool, content in iter_func(BaseServiceHelper.stream(msg_list, self.llm_handler)):
            b = Block()
            fragment = ReplyMessageFragment()
            fragment.message_id = self._reply_msg_id
            fragment.role = Role.ASSISTANT
            fragment.created_at = common.get_time()
            fragment.content = content
            b.message = fragment
            b.conversation_id = str(self._conversation.cid)
            b.group_id = self._conversation.biz_id  # type: ignore
            b.generation_mode = None
            b.source = self._get_block_source()

            if last_bool:
                b.finished = True
                b.generation_mode = generation_mode
                self._on_last_block(b, generation_mode)
            yield b
        logging.info(timer.tik_with_desc(f"【{self._name}】大模型回复"))
