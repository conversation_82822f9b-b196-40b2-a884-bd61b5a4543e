import json
import logging
from typing import Op<PERSON>

from elasticsearch_dsl import Search

from rocket_util import BizException
from rocket_util.common import es_intent_index_name

from quickrag.service.base import BaseServiceHelper
from quickrag.service.retrieval.service_retrieval import BaseRetrievalService
from quickrag.service.query import (
    QueryIntentRobotConfService,
    QueryAreaRobotRelationService,
    QueryIntentCategoryListService,
    QueryIntentConfService
)
from quickrag.schema import (
    IntentRetrievalResult,
    MatchTextExpr,
    MatchDenseExpr,
    IntentChunk,
    IntentLevel
)
from quickrag.schema import IntentCategoryDTO


class IntentRetrievalService(BaseRetrievalService):
    select_field_template = [
        "category_id",
        "intent_data_id",
        "intent_id",
        "content_tks",
        "content_ltks",
        "content_with_weight",
    ]

    def __init__(
        self, **kwargs
    ):
        super().__init__(**kwargs)

        self._category_list: Optional[list[IntentCategoryDTO]] = None

    async def _load(self):
        await self._load_robot()
        self._llm_handler = await BaseServiceHelper.create_llm_handler(
            self._db, self._robot.chat_llm_id, self._robot.chat_llm_setting, "intent", self._robot.id
        )
        await self._load_category_list()

    async def _load_category_list(self):
        service = QueryIntentCategoryListService()
        self._category_list = await service.query(
            self._db, self._role, self._province_code, self._city_code, self._robot.category_ids, self._robot.tenant_id
        )

    async def _load_robot(self):
        if not self._robot_id:
            query_service = QueryAreaRobotRelationService()
            area_robot_conf = await query_service.query(
                self._db, self._province_code, self._city_code
            )
            self._robot_id = area_robot_conf.intent_robot_id

        if not self._robot_id:
            raise BizException("robot_id不能为空")

        query_robot_service = QueryIntentRobotConfService()
        self._robot = await query_robot_service.query(self._db, self._robot_id)
        self._robot_chat_setting = self._robot.chat_setting

    async def _retrieval(self, query: str) -> IntentRetrievalResult:
        # 没有相关知识库，返回空
        if not self._category_list:
            return IntentRetrievalResult(total=0, chunks=[])

        if not query:
            return IntentRetrievalResult(total=0, chunks=[])

        res = await self._search(query)
        res = await self._filter_es_res(res)
        return res

    async def _search(self, query: str) -> IntentRetrievalResult:
        tenant_id = self._robot.tenant_id
        category_ids = [c.id for c in self._category_list]
        limit = self._robot.top_n
        top_k = self._robot.top_k
        similarity = self._robot.similarity_threshold

        r, _ = self._queryer.question(query, min_match=0.3, fields=self._get_to_match_fields())
        match_text = MatchTextExpr(**r)
        match_dense = await self._build_vector_expr(query, top_k, similarity) # type: ignore

        search_es = await self._query_es(
            es_intent_index_name(tenant_id), match_text, match_dense,
            {"category_id": category_ids, "status": 1}, limit, 0 # type: ignore
        )
        total = await self._get_total(search_es)
        select_fields = self._get_to_select_fields()
        value_list = await self._get_fields(search_es, select_fields)
        if self._robot_chat_setting.enable_similarity_intent_search:
            raise BizException("不支持设置相似意图搜索")
        chunks = []
        for v in value_list.values():
            c = IntentChunk()
            c.intent_data_id = v.get("intent_data_id", "")
            c.category_id = v.get("category_id", "")

            c.intent_level_2_id = v.get("intent_id", "")

            c.content_tks = v.get("content_tks", "")
            c.content_ltks = v.get("content_ltks", "")
            c.content_with_weight = v.get("content_with_weight", "")

            chunks.append(c)

        return IntentRetrievalResult(total=total, chunks=chunks)

    async def _query_es(
        self, index_name: str, match_text: MatchTextExpr, match_dense: MatchDenseExpr,
        conditions: dict, limit: int, offset: int
    ) -> dict:
        s = Search()

        min_match = self._robot_chat_setting.keyword_min_match
        knn_filter_bool_query = self._build_bool_query(match_text, conditions, min_match)
        s = s.knn(
            match_dense.vector_column_name,
            match_dense.topn,
            match_dense.topn * 2,
            query_vector=list(match_dense.embedding_data),
            filter=knn_filter_bool_query.to_dict(),
            similarity=match_dense.extra_options.get("similarity", 0.75),
        )

        if limit > 0:
            s = s[offset: offset + limit]

        q = s.to_dict()
        logging.debug(f"ESStore.search {index_name} query: {json.dumps(q)}")
        return await self._query_es_once(self._data_store, index_name, q)

    def _get_to_select_fields(self):
        return list(self.select_field_template)

    def _get_to_match_fields(self):
        return self._robot_chat_setting.field_weight_list

    async def _filter_es_res(self, res: IntentRetrievalResult) -> IntentRetrievalResult:
        """过滤掉所有失效的意图信息"""
        if res.total == 0:
            return res

        # 1. 从ES结果中提取所有的 level_2_id
        level_2_id_list = [int(c.intent_level_2_id) for c in res.chunks]

        valid_intent_2_list = await QueryIntentConfService.query(self._db, level_2_id_list, IntentLevel.LEVEL_2)
        if not valid_intent_2_list:
            return IntentRetrievalResult(total=0, chunks=[])

        # 构建二级意图映射：intent_id -> Intent对象
        intent_2_dict = {str(i.id): i for i in valid_intent_2_list}

        # 3. 查询所有相关的一级意图
        level_1_id_list = [i.parent_id for i in valid_intent_2_list]
        valid_intent_1_list = await QueryIntentConfService.query(self._db, level_1_id_list, IntentLevel.LEVEL_1)
        if not valid_intent_1_list:
            return IntentRetrievalResult(total=0, chunks=[])

        # 构建一级意图映射：intent_id -> Intent对象
        intent_1_dict = {str(i.id): i for i in valid_intent_1_list}

        # 4. 遍历原始 chunks，过滤无效的，补全一级/二级信息
        valid_chunks = []
        for chunk in res.chunks:
            level_2 = intent_2_dict.get(chunk.intent_level_2_id, None) # noqa
            if not level_2:
                continue  # 二级意图无效
            level_1 = intent_1_dict.get(str(level_2.parent_id), None)
            if not level_1:
                continue  # 对应的一级意图无效

            # 补全信息
            chunk.intent_level_2_name = level_2.name
            chunk.intent_level_2_desc = level_2.desc
            chunk.intent_level_1_id = str(level_1.id)
            chunk.intent_level_1_name = level_1.name
            chunk.intent_level_1_desc = level_1.desc

            valid_chunks.append(chunk)

        return IntentRetrievalResult(total=len(valid_chunks), chunks=valid_chunks)
