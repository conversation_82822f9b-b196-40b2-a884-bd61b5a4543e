import logging
import re
import ast
import json
from typing import Optional

import numpy as np
from elasticsearch_dsl import Search

from rocket_util.common import es_qa_index_name
from rocket_util import Timer, BizException
from rocket_nlp import rag_tokenizer

from qr_orm.const import (
    KB_TYPE_FAQ,
    KB_TYPE_DOCUMENT,
    SCOPE_PROVINCE,
    SCOPE_CITY,
    SCOPE_GLOBAL
)
from qr_orm import const as orm_const

from quickrag.service.base import BaseServiceHelper
from quickrag.service.query import (
    QueryAreaRobotRelationService,
    QueryQARobotConfService,
    QueryKbListService
)
from quickrag.schema import (
    QARetrievalResult,
    Role,
    QASearchResult,
    QASearchReq,
    QAChunk,
    WeightGroup,
    MatchDenseExpr,
    MatchTextExpr,
    AreaDTO,
    AreaInfo,
    KbDTO
)
from quickrag.service.retrieval.service_retrieval import BaseRetrievalService
from quickrag.core import manager, CacheKey


class KnowledgeRetrievalService(BaseRetrievalService):
    """知识检索服务"""

    select_field_template = [
        "docnm_kwd",
        "content_ltks",
        "kb_id",
        "img_id",
        "title_tks",
        "important_kwd",
        "position_int",
        "doc_id",
        "page_num_int",
        "top_int",
        "create_timestamp_flt",
        "knowledge_graph_kwd",
        "question_kwd",
        "question_tks",
        "available_int",
        "content_with_weight",
        "kb_type",
        "extra",
    ]

    DOC_SUFFIX_PATTERN = re.compile(r".docx$")

    def __init__(self, tag_id_list: list[int], **kwargs):
        super().__init__(**kwargs)
        # 传入的参数
        self._tag_id_list: list[int] = tag_id_list

        # 定义的参数
        self._kb_list: Optional[list[KbDTO]] = None

    async def _load(self):
        await self._load_robot()
        self._llm_handler = await BaseServiceHelper.create_llm_handler(
            self._db, self._robot.chat_llm_id, self._robot.chat_llm_setting, "qa", self._robot.id
        )
        tag_match_timer = Timer()
        await self._load_kb_list()
        logging.info(tag_match_timer.tik_with_desc(f"【{self._name}】标签匹配"))

    async def _load_robot(self):
        if not self._robot_id:
            query_service = QueryAreaRobotRelationService()
            area_robot_conf = await query_service.query(
                self._db, self._province_code, self._city_code
            )
            self._robot_id = area_robot_conf.qa_robot_id

        if not self._robot_id:
            raise BizException("robot_id不能为空")

        self._robot = await QueryQARobotConfService().query(self._db, self._robot_id)
        self._robot_chat_setting = self._robot.chat_setting

    async def _load_kb_list(self):
        service = QueryKbListService()
        self._kb_list = await service.query(
            self._db, self._province_code, self._city_code, self._robot.kb_ids,
            self._tag_id_list, self._robot.tenant_id, self._origin_query
        )

    async def _retrieval(self, query: str) -> QARetrievalResult:
        tenant_id = self._robot.tenant_id
        # 没有相关知识库，返回空
        if not self._kb_list:
            return QARetrievalResult(total=0, chunks=[])

        if not query:
            return QARetrievalResult(total=0, chunks=[])

        kb_ids = [kb.id for kb in self._kb_list]
        req = QASearchReq(
            kb_ids=kb_ids,
            limit=max(self._robot.top_n * 3, 128),
            question=query,
            topk=self._robot.top_k, # type: ignore
            similarity=self._robot.similarity_threshold, # type: ignore
            available_int=1,
        )

        search_res = await self._search(req, es_qa_index_name(tenant_id))

        if search_res.total == 0:
            return QARetrievalResult(total=0, chunks=[])

        sim, tk_sim, vec_sim = self._rerank(
            search_res,
            query,
        )

        ret = QARetrievalResult(total=0, chunks=[])
        idx = np.argsort(-sim)

        dim = len(search_res.query_vector)
        vector_column = f"q_{dim}_vec"
        zero_vector = [0.0] * dim

        kb_dict = {kb.id: kb for kb in self._kb_list}

        for i in idx:
            if sim[i] < self._robot.rerank_threshold:
                continue
            if len(ret.chunks) >= self._robot.top_n:
                break

            chunk_id = search_res.chunk_ids[i]
            chunk = search_res.data[chunk_id]
            extra = self._parse_extra(chunk.get("extra", {}))
            kb_type = chunk.get("kb_type", orm_const.KB_TYPE_HISTORY)
            biz_id = extra.get(
                "group_id" if kb_type == orm_const.KB_TYPE_HISTORY else "knowledge_id",
                "",
            )
            doc_name = self._transform_doc_name(
                chunk.get("docnm_kwd", ""), biz_id, kb_type, chunk['kb_id'], kb_dict
            )

            qa_chunk = QAChunk(
                chunk_id=chunk_id,
                content_ltks=chunk["content_ltks"],
                content_with_weight=chunk["content_with_weight"],
                doc_id=chunk.get("doc_id", ""),
                doc_kwd=doc_name,
                kb_id=chunk["kb_id"],
                important_kwd=chunk.get("important_kwd", []),
                image_id=chunk.get("img_id", ""),
                similarity=sim[i],
                vector_similarity=vec_sim[i],
                term_similarity=tk_sim[i],
                vector=chunk.get(vector_column, zero_vector),
                positions=chunk.get("position_int", []),
                kb_type=kb_type,
                extra=extra,
            )

            ret.chunks.append(qa_chunk)
            ret.total += 1

        return ret

    async def _search(self, req: QASearchReq, idx_name: str) -> QASearchResult:
        conditions = self._build_filter_conditions(req)
        qst, topk, similarity = req.question, req.topk, req.similarity

        r, keywords = self._queryer.question(qst, min_match=0.3, fields=self._get_to_match_fields())

        if not r:
            return QASearchResult(total=0, chunk_ids=[], query_vector=[], data={}, keywords=[])

        match_text = MatchTextExpr(**r)

        match_dense = await self._build_vector_expr(qst, topk, similarity)
        q_vec = match_dense.embedding_data

        select_fields = self._get_to_select_fields()
        select_fields.append(f"q_{len(q_vec)}_vec")

        data = await self._query_es(conditions, match_text, match_dense, 0, req.limit, idx_name)
        total = await self._get_total(data)
        chunk_ids = await self._get_ids(data)
        keywords = self._expand_keywords(keywords)

        return QASearchResult(
            total=total,
            chunk_ids=chunk_ids,
            query_vector=q_vec,
            data=await self._get_fields(data, select_fields),
            keywords=keywords,
        )

    def _build_filter_conditions(self, req: QASearchReq) -> dict:
        role = self._role
        conditions = {
            "kb_id": req.kb_ids,
            "available_int": req.available_int,
            "target_role": [Role.USER.value] if role == Role.USER else [Role.USER.value, Role.EXPERT.value]
        }
        return conditions

    @classmethod
    def _expand_keywords(cls, keywords: list[str]) -> list[str]:
        kwd_set = set(keywords)
        for k in keywords:
            for token in rag_tokenizer.fine_grained_tokenize(k).split():
                if len(token) >= 2:
                    kwd_set.add(token)
        return list(kwd_set)

    def _rerank(
        self,
        search_res: QASearchResult,
        query: str,
        c_field: str = "content_ltks",
    ):
        kb_list = self._kb_list
        weight_group = self._robot_chat_setting.weight_group
        vector_similarity_weight = self._robot.vector_similarity_weight
        tk_weight = 1 - vector_similarity_weight
        _, keywords = self._queryer.question(query)
        vector_column = f"q_{len(search_res.query_vector)}_vec"
        zero_vector = [0.0] * len(search_res.query_vector)

        ins_embd = [
            self._parse_vector(search_res.data[i].get(vector_column, zero_vector))
            for i in search_res.chunk_ids
        ]

        ins_tw = [
            self._build_token_weight_vector(search_res.data[i], c_field)
            for i in search_res.chunk_ids
        ]

        sim, tk_sim, vec_sim = self._queryer.hybrid_similarity(
            search_res.query_vector,
            ins_embd,
            keywords,
            ins_tw,
            tk_weight,
            vector_similarity_weight,
        )

        weights = np.array(
            [
                self._calculate_final_weight(weight_group, kb_list, search_res.data[i])
                for i in search_res.data
            ]
        )
        return sim * weights, tk_sim, vec_sim

    @classmethod
    def _parse_vector(cls, vec) -> list[float]:
        if isinstance(vec, str):
            return [float(v) for v in vec.split("\t")]
        return vec

    @classmethod
    def _build_token_weight_vector(cls, chunk_data: dict, c_field: str) -> list[str]:
        if isinstance(chunk_data.get("important_kwd", []), str):
            chunk_data["important_kwd"] = [chunk_data["important_kwd"]]
        return (
            chunk_data[c_field].split()
            + chunk_data.get("title_tks", "").split() * 2
            + chunk_data.get("important_kwd", []) * 5
            + chunk_data.get("question_tks", "").split() * 6
        )

    def _calculate_final_weight(self, weight_group, kb_list, chunk) -> float:
        kb_dict = {kb.id: kb for kb in kb_list}
        kb = kb_dict.get(chunk.get("kb_id", 0), None)
        if not kb:
            return 1.0
        return self._calculate_weight(weight_group, kb.scope, kb.type, kb.weight)

    def _transform_doc_name(
        self, name: str, biz_id: str, kb_type: str, kb_id: str, kb_dict: dict[str, KbDTO]
    ) -> str:
        name: str
        if kb_type == orm_const.KB_TYPE_FAQ:
            name = self.DOC_SUFFIX_PATTERN.sub("", name.replace(f"{biz_id}_", "【{area_name}知识】"))
        elif kb_type == orm_const.KB_TYPE_DOCUMENT:
            name = "【{area_name}文档】" + name
        else:
            name = self.DOC_SUFFIX_PATTERN.sub("", name.replace(f"{biz_id}_", "【{area_name}会话】"))

        if kb_id not in kb_dict:
            return name.replace("{area_name}", "")

        kb = kb_dict[kb_id] # noqa

        area: AreaDTO | None = self.get_area(kb.scope, kb.province_code, kb.city_code)
        if not area or not area.area_name:
            return name.replace("{area_name}", "")

        return name.replace("{area_name}", f"{area.area_name}-")

    @classmethod
    def _calculate_weight(
        cls, weight_group: WeightGroup, kb_scope: int, kb_type: str, kb_weight: float
    ) -> float:
        # 计算范围权重
        if kb_scope == orm_const.SCOPE_GLOBAL:
            scope_weight = weight_group.scope.global_
        elif kb_scope == orm_const.SCOPE_PROVINCE:
            scope_weight = weight_group.scope.province
        else:
            scope_weight = weight_group.scope.city

        # 计算类型权重
        if kb_type == KB_TYPE_FAQ:
            type_weight = weight_group.type.faq
        elif kb_type == KB_TYPE_DOCUMENT:
            type_weight = weight_group.type.document
        else:
            type_weight = weight_group.type.history

        return kb_weight * scope_weight * type_weight

    @classmethod
    def _parse_extra(cls, extra_raw):
        if isinstance(extra_raw, str):
            try:
                return ast.literal_eval(extra_raw)
            except Exception as e:
                logging.error(f"extra parse error: {e}")
        return extra_raw

    async def _query_es(
        self,
        conditions: dict,
        match_text: MatchTextExpr,
        match_dense: MatchDenseExpr,
        offset: int,
        limit: int,
        index_name: str,
    ):
        min_match = self._robot_chat_setting.keyword_min_match
        bool_query = self._build_bool_query(match_text, conditions, min_match)
        s = Search()
        s = s.knn(
            match_dense.vector_column_name,
            match_dense.topn,
            match_dense.topn * 2,
            query_vector=list(match_dense.embedding_data),
            filter=bool_query.to_dict(),
            similarity=match_dense.extra_options.get("similarity", 0.5),
        )
        # 增加关键词路的权重
        bool_query.boost = self._robot_chat_setting.keyword_weight
        s = s.query(bool_query)
        if limit > 0:
            s = s[offset: offset + limit]

        q = s.to_dict()
        logging.debug(f"ESStore.search {index_name} query: {json.dumps(q)}")
        return await self._query_es_once(self._data_store, index_name, q)

    def _get_to_select_fields(self):
        return list(self.select_field_template)

    def _get_to_match_fields(self):
        return self._robot_chat_setting.field_weight_list

    @classmethod
    def get_area(cls, scope: int, province_code: str | None =None, city_code: str | None = None) -> AreaDTO | None:
        cache: AreaInfo = manager.get_cache(CacheKey.AREA_INFO)
        if scope == SCOPE_GLOBAL:
            return cache.global_
        elif scope == SCOPE_PROVINCE:
            return cache.province.get(province_code)
        elif scope == SCOPE_CITY:
            return cache.city.get(city_code)
        raise BizException("地域范围错误！")
