import logging
from typing import Any, Union
from abc import abstractmethod, ABC

from elasticsearch_dsl import Q
from sqlalchemy.ext.asyncio import AsyncSession

from rocket_util.common import async_retry
from rocket_util import Timer
from rocket_nlp.query import FulltextQueryer

from quickrag.service.base.service_base import BaseService
from quickrag.service.common.service_embedding import EmbeddingService
from quickrag.schema import MatchDenseExpr, MatchTextExpr, Role
from quickrag.core import ESStore


class BaseRetrievalService(BaseService, ABC):

    def __init__(
        self,
        name,
        robot_id: int,
        db: AsyncSession,
        province_code: str,
        city_code: str,
        role: Role,
        account: str,
        origin_query: str,
        group_id: str,
        data_store: ESStore,
        queryer: FulltextQueryer,
    ):
        self._name = name
        self._robot_id = robot_id
        self._db = db
        self._province_code: str = province_code
        self._city_code: str = city_code
        self._role: Role = role
        self._account: str = account
        self._origin_query: str = origin_query
        self._group_id: str = group_id

        self._queryer: FulltextQueryer = queryer
        self._data_store: ESStore = data_store

        self._robot = None
        self._llm_handler = None
        self._robot_chat_setting = None

    @property
    def robot_id(self):
        return self._robot_id

    @property
    def llm_handler(self):
        return self._llm_handler

    @property
    def robot_chat_setting(self):
        return self._robot_chat_setting

    @property
    def robot(self):
        return self._robot

    async def load(self):
        load_timer = Timer()
        await self._load()
        logging.info(load_timer.tik_with_desc(f"【{self._name}】检索信息加载"))

    async def retrieval(self, query: str):
        retrieval_timer = Timer()
        res = await self._retrieval(query)
        logging.info(retrieval_timer.tik_with_desc(f"【{self._name}】检索"))
        return res

    @abstractmethod
    async def _load(self):
        pass

    @abstractmethod
    async def _retrieval(self, query: str):
        pass

    @abstractmethod
    def _get_to_select_fields(self):
        pass

    @abstractmethod
    def _get_to_match_fields(self):
        pass

    @classmethod
    async def _build_vector_expr(
        cls, text: str, topk: int, similarity: float
    ) -> MatchDenseExpr:
        vector = await EmbeddingService().encode(text)
        return MatchDenseExpr(
            vector_column_name=f"q_{len(vector)}_vec",
            embedding_data=vector,
            embedding_data_type="float",
            distance_type="cosine",
            topn=topk,
            extra_options={"similarity": similarity},
        )

    @async_retry(attempts=3, delay=1)
    async def _query_es_once(self, data_store, index_name: str, q: dict):
        res = await data_store.es.search(
            index=index_name,
            body=q,
            timeout="600s",
            track_total_hits=True,
            _source=True,
        )
        if str(res.get("timed_out", "")).lower() == "true":
            raise Exception("ES Timeout.")
        logging.debug(f"ESStore.search {index_name} res: {str(res)}")
        return res

    @classmethod
    async def _get_total(cls, res) -> int:
        """从 Elasticsearch 查询结果中获取命中总数."""
        total = res.get("hits", {}).get("total", 0)
        if isinstance(total, dict):
            return total.get("value", 0)
        return total

    @classmethod
    async def _get_ids(cls, res) -> list[str]:
        """从 Elasticsearch 查询结果中提取所有文档的 _id 列表."""
        hits = res.get("hits", {}).get("hits", [])
        return [hit.get("_id") for hit in hits if "_id" in hit]

    @classmethod
    async def _get_sources(cls, res) -> list[dict]:
        """
        从 Elasticsearch 查询结果中提取所有文档的 _source 列表，
        并附加字段 id 和 _score 到每个 _source 中。
        """
        hits = res.get("hits", {}).get("hits", [])
        sources = []
        for hit in hits:
            source = hit.get("_source", {}).copy()
            source["id"] = hit.get("_id")
            source["_score"] = hit.get("_score")
            sources.append(source)
        return sources

    @classmethod
    async def _get_fields(
        cls, res, fields: list[str]
    ) -> dict[str, dict[str, Union[str, list[Any]]]]:
        """
        从 Elasticsearch 查询结果中提取指定字段内容，结果以 {id: {field: value}} 形式返回。

        对非字符串类型转换为字符串，列表类型保持不变。
        """
        if not fields:
            return {}

        res_fields = {}
        sources = await cls._get_sources(res)
        for source in sources:
            filtered = {}
            for field in fields:
                value = source.get(field)
                if value is None:
                    continue
                if isinstance(value, list):
                    filtered[field] = value
                else:
                    filtered[field] = value if isinstance(value, str) else str(value)
            if filtered:
                res_fields[source["id"]] = filtered
        return res_fields

    @classmethod
    def _build_bool_query(cls, match_text: MatchTextExpr, conditions: dict, min_match: float = 0.3):
        bool_query = Q("bool", must=[])

        for k, v in conditions.items():
            if k == "available_int":
                bool_query.filter.append(
                    Q("bool", must_not=Q("range", available_int={"lt": 1}))
                    if v
                    else Q("range", available_int={"lt": 1})
                )
            elif v:
                bool_query.filter.append(
                    Q("terms" if isinstance(v, list) else "term", **{k: v})
                )

        minimum_should_match = f"{int(min_match * 100)}%"

        bool_query.must.append(
            Q(
                "query_string",
                fields=match_text.fields,
                type="best_fields",
                query=match_text.matching_text,
                minimum_should_match=minimum_should_match,
                boost=1,
            )
        )
        return bool_query
