import asyncio
from asyncio import Queue
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession

from rocket_util import B<PERSON>Exception

from quickrag.schema import Chat<PERSON><PERSON><PERSON>, UseMode
from quickrag.service.surface.chat.service_chat import BaseChatService
from quickrag.service.rag import QARagService, IntentRagService


class CommonChatService(BaseChatService):
    def __init__(self, request: ChatRequest, db: Optional[AsyncSession] = None):
        super(CommonChatService, self).__init__(request, db)

        # 两个核心服务的定义
        self._qa_service: Optional[QARagService] = None
        self._intent_service: Optional[IntentRagService] = None

    def _init_services(self):
        self._qa_service = QARagService(
            self,
            self._request.email,
            name="QA",
            tag_id_list=self._request.tag_id_list,
            conversation=self._conversation,
            db=self._db,
            group_id=self._request.group_id,
            account=self._request.account,
            origin_query=self._request.query,
            province_code=self._request.province_code,
            city_code=self._request.city_code,
            role=self._request.role,
        )

        self._intent_service = IntentRagService(
            name="Intent",
            conversation=self._conversation,
            db=self._db,
            group_id=self._request.group_id,
            account=self._request.account,
            origin_query=self._request.query,
            province_code=self._request.province_code,
            city_code=self._request.city_code,
            role=self._request.role,
        )

    def _create_task_list(self, block_queue: Queue) -> list:
        task_list = []
        if self._request.mode == UseMode.QA:
            task_list.append(asyncio.create_task(self._qa_service.rag(block_queue)))
        elif self._request.mode == UseMode.INTENT:
            task_list.append(asyncio.create_task(self._intent_service.rag(block_queue)))
        elif self._request.mode == UseMode.BOTH:
            task_list.append(asyncio.create_task(self._qa_service.rag(block_queue)))
            task_list.append(asyncio.create_task(self._intent_service.rag(block_queue)))
        else:
            raise BizException("使用模式错误！")
        return task_list
