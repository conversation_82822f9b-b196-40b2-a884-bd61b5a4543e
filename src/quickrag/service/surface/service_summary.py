import json
import re
import logging

from langchain_core.output_parsers import PydanticOutputParser
from langchain.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from rocket_util import BizException

from quickrag.schema import SummaryResult, SummaryRequest
from quickrag.core import manager
from quickrag.service.base import BaseService, BaseServiceHelper
from quickrag.schema.schema_base import ChatModel
from quickrag.service.query import QueryCommonConfService, QueryLLMConfService


BLANK_PATTERN = r"\s+"


class Result(BaseModel):
    title: str = Field("", description="标题", max_length=50)
    content: str = Field("", description="内容", max_length=500)
    biz_number: str = Field("", description="业务单号")


class SummaryService(BaseService):
    async def summary(self, db: AsyncSession, request: SummaryRequest) -> SummaryResult:
        if not request.msg_list:
            raise BizException("消息列表为空！")

        common_conf = await QueryCommonConfService.query(db)
        llm_conf = await QueryLLMConfService.query(db, common_conf.default_llm_id)

        llm: ChatModel = BaseServiceHelper.load_chat_model(llm_conf, {}, "common", 0)
        if not llm:
            raise BizException(f"大模型未配置，id：{llm_conf.llm_id}")

        return await self._summary(request, llm)

    async def _summary(self, request: SummaryRequest, llm: ChatModel) -> SummaryResult:
        prompt = self.construct_prompt()
        parser = PydanticOutputParser(pydantic_object=Result)
        chain = prompt | llm | parser

        human_msg_list = []
        for content in request.msg_list:
            content = re.sub(BLANK_PATTERN, "", content)
            if content:
                human_msg_list.append(content)

        content = json.dumps(human_msg_list, indent=2, ensure_ascii=False)

        try:
            res = await chain.ainvoke({"output_format": parser.get_format_instructions(), "msg_list": content})
            return SummaryResult(title=res.title, content=res.content, biz_number=res.biz_number)
        except Exception as e:
            logging.error("调用大模型生成转专家信息总结失败！", exc_info=e)
            return SummaryResult()

    @classmethod
    def construct_prompt(cls):
        system_prompt = manager.prompts['summary']
        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", system_prompt),
            ]
        )
        return prompt
