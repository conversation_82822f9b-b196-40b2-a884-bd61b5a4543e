from quickrag.service.base import BaseService
from quickrag.core import manager, <PERSON><PERSON><PERSON><PERSON>
from quickrag.schema import GetAvailableTagRequest, AvailableTag, TagKeywordInfo


class TagService(BaseService):
    def __init__(self):
        pass

    @classmethod
    async def query_available_tag_list(cls, request: GetAvailableTagRequest) -> list[AvailableTag]:
        tag_map = {}
        cache: TagKeywordInfo = manager.get_cache(CacheKey.TAG_KEYWORD)
        province_code = request.province_code
        city_code = request.city_code

        # province_code或city_code缺失
        if not province_code or not city_code:
            return []

        tag_global = cache.tag_map.global_
        tag_province = cache.tag_map.province
        tag_city = cache.tag_map.city

        for tag in tag_global.values():
            tag_map[tag.tag_id] = AvailableTag(tag_id=str(tag.tag_id), tag_name=tag.tag_name, tag_desc=tag.tag_desc)

        if province_code in tag_province:
            for tag in tag_province[province_code].values():
                tag_map[tag.tag_id] = AvailableTag(tag_id=str(tag.tag_id), tag_name=tag.tag_name, tag_desc=tag.tag_desc)

        if city_code in tag_city:
            for tag in tag_city[city_code].values():
                tag_map[tag.tag_id] = AvailableTag(tag_id=str(tag.tag_id), tag_name=tag.tag_name, tag_desc=tag.tag_desc)

        return list(tag_map.values())
