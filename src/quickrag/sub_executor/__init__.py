from enum import StrEnum


from quickrag.sub_executor.reload_keyword_executor import Reload<PERSON><PERSON>wordExecutor
from quickrag.sub_executor.reload_area_info_executor import Reload<PERSON>reaInfoExecutor
from quickrag.sub_executor.reload_area_robot_relation_executor import ReloadAreaRobotRelationExecutor
from quickrag.sub_executor.reload_qa_robot_conf_executor import ReloadQARobotConfExecutor
from quickrag.sub_executor.reload_intent_robot_conf_executor import ReloadIntentRobotConfExecutor
from quickrag.sub_executor.reload_common_conf_executor import ReloadCommonConfExecutor
from quickrag.sub_executor.reload_llm_conf_executor import ReloadLLMConfExecutor
from quickrag.sub_executor.reload_intent_map_executor import ReloadIntentMapExecutor
from quickrag.sub_executor.reload_intent_tool_map_executor import ReloadIntentToolMapExecutor
from quickrag.sub_executor.reload_kb_map_executor import Reload<PERSON>bMapExecutor
from quickrag.sub_executor.reload_conf import ReloadConf


class ExecutorType(StrEnum):
    """
    订阅执行器类型
    """
    RELOAD_KEYWORD = "reload_keyword"
    RELOAD_AREA_INFO = "reload_area_info"
    RELOAD_AREA_ROBOT_RELATION = "reload_area_robot_relation"
    RELOAD_QA_ROBOT_CONF = "reload_qa_robot_conf"
    RELOAD_INTENT_ROBOT_CONF = "reload_intent_robot_conf"
    RELOAD_COMMON_CONF = "reload_common_conf"
    RELOAD_LLM_CONF = "reload_llm_conf"
    RELOAD_INTENT_MAP = "reload_intent_map"
    RELOAD_INTENT_TOOL_MAP = "reload_intent_tool_map"
    RELOAD_KB_MAP = "reload_kb_map"

    RELOAD_CONF = "reload_conf"


executors = {
    ExecutorType.RELOAD_KEYWORD: ReloadKeywordExecutor(),
    ExecutorType.RELOAD_AREA_INFO: ReloadAreaInfoExecutor(),
    ExecutorType.RELOAD_AREA_ROBOT_RELATION: ReloadAreaRobotRelationExecutor(),
    ExecutorType.RELOAD_QA_ROBOT_CONF: ReloadQARobotConfExecutor(),
    ExecutorType.RELOAD_INTENT_ROBOT_CONF: ReloadIntentRobotConfExecutor(),
    ExecutorType.RELOAD_COMMON_CONF: ReloadCommonConfExecutor(),
    ExecutorType.RELOAD_LLM_CONF: ReloadLLMConfExecutor(),
    ExecutorType.RELOAD_INTENT_MAP: ReloadIntentMapExecutor(),
    ExecutorType.RELOAD_INTENT_TOOL_MAP: ReloadIntentToolMapExecutor(),
    ExecutorType.RELOAD_KB_MAP: ReloadKbMapExecutor(),
    ExecutorType.RELOAD_CONF: ReloadConf(),
}


__all__ = [
    "executors"
]
