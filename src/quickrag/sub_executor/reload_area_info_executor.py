from rocket_core.redis_client import SubExecutor
from quickrag.core import manager, db_util, <PERSON><PERSON><PERSON><PERSON>
from quickrag.service.load_cache.service_load_area_info import LoadAreaInfoService


class ReloadAreaInfoExecutor(SubExecutor):
    async def execute(self, data):
        service = LoadAreaInfoService()
        async with db_util.AsyncSessionLocal() as conn:
            info = await service.load(conn)
        manager.reset_cache(CacheKey.AREA_INFO, info)


__all__ = ["ReloadAreaInfoExecutor"]