from rocket_core.redis_client import SubExecutor
from quickrag.core import manager, db_util, <PERSON><PERSON><PERSON><PERSON>
from quickrag.service.load_cache.service_load_area_robot_relation import LoadAreaRobotRelationService


class ReloadAreaRobotRelationExecutor(SubExecutor):
    async def execute(self, data):
        service = LoadAreaRobotRelationService()
        async with db_util.AsyncSessionLocal() as conn:
            info = await service.load(conn)
        manager.reset_cache(CacheKey.AREA_ROBOT_RELATION, info)


__all__ = ["ReloadAreaRobotRelationExecutor"]
