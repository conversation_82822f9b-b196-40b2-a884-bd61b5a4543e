from rocket_core.redis_client import SubExecutor
from quickrag.core import manager, db_util, <PERSON><PERSON><PERSON><PERSON>
from quickrag.service.load_cache import LoadCommonConfService


class ReloadCommonConfExecutor(SubExecutor):
    async def execute(self, data):
        service = LoadCommonConfService()
        async with db_util.AsyncSessionLocal() as conn:
            info = await service.load(conn)
        manager.reset_cache(CacheKey.COMMON, info)


__all__ = ["ReloadCommonConfExecutor"]
