from rocket_core.redis_client import SubExecutor
from quickrag.sub_executor.reload_keyword_executor import ReloadKeywordExecutor
from quickrag.sub_executor.reload_area_info_executor import ReloadAreaInfoExecutor
from quickrag.sub_executor.reload_area_robot_relation_executor import ReloadAreaRobotRelationExecutor
from quickrag.sub_executor.reload_intent_robot_conf_executor import ReloadIntentRobotConfExecutor
from quickrag.sub_executor.reload_qa_robot_conf_executor import ReloadQARobotConfExecutor
from quickrag.sub_executor.reload_llm_conf_executor import ReloadLL<PERSON>onfExecutor
from quickrag.sub_executor.reload_common_conf_executor import ReloadCommonConfExecutor
from quickrag.sub_executor.reload_intent_map_executor import ReloadIntentMapExecutor
from quickrag.sub_executor.reload_intent_tool_map_executor import ReloadIntentToolMapExecutor
from quickrag.sub_executor.reload_kb_map_executor import ReloadKbMapExecutor


class ReloadConf(SubExecutor):

    async def execute(self, data):
        reload_service_list = [
            ReloadAreaInfoExecutor(),
            ReloadAreaRobotRelationExecutor(),
            ReloadKeywordExecutor(),
            ReloadIntentRobotConfExecutor(),
            ReloadQARobotConfExecutor(),
            ReloadCommonConfExecutor(),
            ReloadLLMConfExecutor(),
            ReloadIntentMapExecutor(),
            ReloadIntentToolMapExecutor(),
            ReloadKbMapExecutor()
        ]

        for reload_service in reload_service_list:
            await reload_service.execute(data)


__all__ = [
    "ReloadConf"
]