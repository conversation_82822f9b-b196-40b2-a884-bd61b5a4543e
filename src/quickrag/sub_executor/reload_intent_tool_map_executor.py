from rocket_core.redis_client import SubExecutor
from quickrag.core import manager, db_util, <PERSON><PERSON><PERSON><PERSON>
from quickrag.service.load_cache import LoadIntentToolMapService


class ReloadIntentToolMapExecutor(SubExecutor):
    async def execute(self, data):
        service = LoadIntentToolMapService()
        async with db_util.AsyncSessionLocal() as conn:
            info = await service.load(conn)
        manager.reset_cache(CacheKey.INTENT_SCENE_RELATION, info)


__all__ = ["ReloadIntentToolMapExecutor"]