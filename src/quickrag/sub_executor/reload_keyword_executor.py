from quickrag.service.load_cache import LoadTagKeywordService
from rocket_core.redis_client import SubExecutor
from quickrag.core import manager, db_util, CacheKey


class ReloadKeywordExecutor(SubExecutor):
    async def execute(self, data):
        service = LoadTagKeywordService()
        async with db_util.AsyncSessionLocal() as conn:
            info = await service.load(conn)
        manager.reset_cache(CacheKey.TAG_KEYWORD, info)


__all__ = ["ReloadKeywordExecutor"]