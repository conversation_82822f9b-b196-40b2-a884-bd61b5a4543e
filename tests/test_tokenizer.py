from rocket_nlp.rag_tokenizer import RagTokenizer


def test():
    tokenizer = RagTokenizer(debug=True)
    tks = tokenizer.tokenize(
        "哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈"
    )
    print(tks)

    tks = tokenizer.tokenize(
        "公开征求意见稿提出，境外投资者可使用自有人民币或外汇投资。使用外汇投资的，可通过债券持有人在香港人民币业务清算行及香港地区经批准可进入境内银行间外汇市场进行交易的境外人民币业务参加行（以下统称香港结算行）办理外汇资金兑换。香港结算行由此所产生的头寸可到境内银行间外汇市场平盘。使用外汇投资的，在其投资的债券到期或卖出后，原则上应兑换回外汇。"
    )
    print(tks)

    tks = tokenizer.tokenize(
        "多校划片就是一个小区对应多个小学初中，让买了学区房的家庭也不确定到底能上哪个学校。目的是通过这种方式为学区房降温，把就近入学落到实处。南京市长江大桥"
    )
    print(tks)

    tks = tokenizer.tokenize(
        "实际上当时他们已经将业务中心偏移到安全部门和针对政府企业的部门 Scripts are compiled and cached aaaaaaaaa"
    )
    print(tks)

    tks = tokenizer.tokenize("虽然我不怎么玩")
    print(tks)

    tks = tokenizer.tokenize("蓝月亮如何在外资夹击中生存,那是全宇宙最有意思的")
    print(tks)

    tks = tokenizer.tokenize(
        "涡轮增压发动机num最大功率,不像别的共享买车锁电子化的手段,我们接过来是否有意义,黄黄爱美食,不过，今天阿奇要讲到的这家农贸市场，说实话，还真蛮有特色的！不仅环境好，还打出了"
    )
    print(tks)

    tks = tokenizer.tokenize("这周日你去吗？这周日你有空吗？")
    print(tks)

    tks = tokenizer.tokenize("Unity3D开发经验 测试开发工程师 c++双11双11 985 211 ")
    print(tks)

    tks = tokenizer.tokenize(
        "数据分析项目经理|数据分析挖掘|数据分析方向|商品数据分析|搜索数据分析 sql python hive tableau Cocos2d-"
    )
    print(tks)


test()
